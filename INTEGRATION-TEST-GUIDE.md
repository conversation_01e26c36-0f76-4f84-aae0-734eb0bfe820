# Guia do Teste de Integração Cypress

Este guia explica como usar o teste de integração automatizado que foi criado para o seu projeto.

## 📋 O que o teste faz

O teste automatiza o seguinte fluxo para cada linha de dados:

1. **Acessa** a página `/access-page`
2. **Preenche** o campo "Número do Pedido" com o primeiro valor da linha
3. **Preenche** o campo "CPF/CNPJ" com o segundo valor da linha  
4. **Clica** no botão "Rastrear Pedido"
5. **Aguarda** 2 segundos na página do dashboard
6. **Faz scroll** para baixo na página
7. **Aguarda** mais 2 segundos
8. **Clica** no botão de logout no header
9. **Repete** para a próxima linha

## 🚀 Como usar

### Passo 1: Preparar os dados

Edite o arquivo `cypress/fixtures/test-data.txt` e adicione seus dados no formato:

```
numero-versao cpf/cnpj
```

**Exemplo:**
```
123456-1 12345678901
789012-2 98765432100
456789-3 11122233344
654321-4 55566677788
```

### Passo 2: Executar o teste

#### Opção A: Modo interativo (recomendado)
```bash
npm run test:integration:open
```

#### Opção B: Modo headless (automático)
```bash
npm run test:integration
```

#### Opção C: Apenas abrir Cypress
```bash
npm run cypress:open
```
Depois selecione `integration-test.cy.ts`

## 📁 Arquivos criados

```
cypress/
├── e2e/
│   ├── integration-test.cy.ts          # Teste principal
│   └── README-integration-test.md      # Documentação detalhada
├── fixtures/
│   ├── test-data.txt                   # Seus dados de teste (edite este)
│   └── test-data-example.txt           # Exemplo de formato
└── support/
    └── commands.ts                     # Comandos customizados
```

## 🛠️ Comandos customizados disponíveis

- `cy.readTestDataFile(path)` - Lê dados de arquivo
- `cy.loginWithData(codigo, cpf)` - Faz login
- `cy.performLogout()` - Faz logout
- `cy.waitAndScroll()` - Aguarda e faz scroll

## ⚙️ Configurações

- **Viewport:** 1280x720 (desktop)
- **Timeout:** 10 segundos para redirecionamentos
- **Interceptação de APIs:** Configurada para evitar erros

## 🔧 Solução de problemas

### "Nenhum dado de teste encontrado"
- Verifique se `cypress/fixtures/test-data.txt` tem dados válidos
- Certifique-se de que as linhas não começam com `#`
- Confirme que há espaço entre os dois valores

### Erro de timeout
- Verifique se a aplicação está rodando em `localhost:3000`
- Confirme se os dados de teste são válidos
- Teste manualmente se o login funciona

### Erro no logout
- O teste detecta automaticamente desktop/mobile
- Verifique se o botão de logout está visível

## 📊 Testes adicionais incluídos

O arquivo também inclui:

- **Validação de componentes:** Testa campos obrigatórios e formato
- **Teste de responsividade:** Mobile, tablet e desktop
- **Teste com dados de exemplo:** Para demonstração

## 🎯 Exemplo completo

1. **Edite** `cypress/fixtures/test-data.txt`:
```
123456-1 12345678901
789012-2 98765432100
```

2. **Execute**:
```bash
npm run test:integration:open
```

3. **Observe** o Cypress executar automaticamente:
   - Login com 123456-1 e 12345678901
   - Scroll e logout
   - Login com 789012-2 e 98765432100
   - Scroll e logout

## 📝 Notas importantes

- **Dados reais:** Use dados de teste válidos do seu sistema
- **Ambiente:** Certifique-se de que a aplicação está rodando
- **Performance:** O teste aguarda entre cada ação para estabilidade
- **Logs:** O Cypress mostra logs detalhados de cada passo

## 🆘 Suporte

Se encontrar problemas:

1. Verifique se a aplicação está rodando
2. Confirme se os dados de teste são válidos
3. Execute primeiro o teste de exemplo
4. Verifique os logs do Cypress para detalhes

---

**Criado com Cypress 14.4.1 para automação de testes de integração**
