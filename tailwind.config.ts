import type { Config } from "tailwindcss";

const config: Config = {
  content: ["./src/**/*.{js,ts,jsx,tsx,mdx}"],
  theme: {
    extend: {
      colors: {
        PormadeGreen: "var(--primary-color-green)",
        PormadeGray: "var(--bg-pormade-gray)",
        bgPrimaryWhite: "var(--bg-primary-white)",
        formLogin: "var(--bg-form-login)",
        textPhaseAccess: "var(--text-gray-login)",
        textInputLogin: "var(--text-input-login)",
        bgGrayInputLogin: "var(--bg-gray-input-login)",
        textButtonLogin: "var(--text-button-login)",
        bgButtonLoginHover: "var(--bg-button-login-hover)",
        shadowButtonLogin: "var(--shadow-button-login)",
        labelInputLoginActive: "var(--label-input-login-active)",
        labelInputLoginInactive: "var(--label-input-login-inactive)",
        textColorPrimary: "var(--text-color-primary)",
        bgOrderNumberLoading: "var(--bg-order-number-loading)",
        textGrayInititalInformation: "var(--text-gray-inititalInformation)",
        bgProgressTimeline: "var(--bg-progress-timeline)",
        bgGreenTimeline: "var(--bg-green-progress-timeline)",
        textColorTitleCard: "var(--text-title-card)",
        bgCardDetailsProposal: "var(--bg-card-details-proposal)",
        borderItemDetailsProposal: "var(--border-items-details-proposal)",
        textButtonDetailsProposal: "var(--text-button-color)",
        textButtonBgActive: "var(--text-button-bg-active)",
        borderDetailsProposal: "var(--border-details-proposal)",
        textObsImage: "var(--text-obs-image)",
        bgButtonDetails: "var(--bg-button-details)",
        circleProgressTimeline: "var(--circle-progress-timeline)",
        circleProgress: "var(--circle-progress)",
        textInactiveProgress: "var(--text-data-inactive-progress)",
        textStageInactive: "var(--text-stage-inactive)",

        bgItemTableDelivery: "var(--bg-items-table-delivery)",
        borderItemTableDelivery: "var(--border-items-table)",

        borderCopy: "var(--border-copy)",
        textCopy: "var(--text-copy)",
      },
      spacing: {
        height: "calc(100vh - 160px)",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      keyframes: {
        jump: {
          "0%, 100%": { transform: "translateY(0) translateX(0)" },
          "50%": { transform: "translateY(-8px) translateX(0)" },
        },
        pulseLoading: {
          "0%, 100%": { backgroundColor: "var(--bg-loading1)" },
          "50%": { backgroundColor: "var(--bg-loading2)" },
        },
      },
      animation: {
        jump: "jump 1s ease-in-out infinite",
        pulseLoading: "pulseLoading 0.5s ease-in-out infinite",
        "spin-slower": "spin 4s linear infinite",
        "spin-even-slower": "spin 6s linear infinite",
        "spin-slowest": "spin 8s linear infinite",
      },

      textShadow: {
        "1px-1px-black": "1px 1px 1px black",
      },

      screens: {
        tl320: "320px",
        tl480: "480px",
        tl380: "380px",
      },
    },
  },
  plugins: [],
};

export default config;
