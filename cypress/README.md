# Testes Cypress - Tracker Frontend

Este diretório contém todos os testes end-to-end (E2E) e de componentes usando Cypress para o projeto Tracker Frontend.

## Estrutura dos Testes

```
cypress/
├── e2e/                    # Testes end-to-end
│   ├── access-page.cy.ts   # Testes da página de acesso
│   ├── dashboard.cy.ts     # Testes do dashboard
│   ├── api.cy.ts          # Testes de API
│   └── responsive.cy.ts   # Testes responsivos
├── component/              # Testes de componentes
│   └── components.cy.tsx   # Exemplos de testes de componentes
├── fixtures/              # Dados de teste
│   ├── users.json         # Dados de usuários para teste
│   └── tracking-data.json # Dados de rastreamento
└── support/               # Configurações e comandos
    ├── commands.ts        # Comandos customizados
    └── e2e.ts            # Configurações gerais
```

## Scripts Disponíveis

- `npm run cypress:open` - Abre a interface gráfica do Cypress
- `npm run cypress:run` - Executa todos os testes em modo headless
- `npm run e2e` - Inicia o servidor e executa os testes E2E
- `npm run e2e:open` - Inicia o servidor e abre a interface do Cypress

## Comandos Customizados

### `cy.login(email, password)`

Realiza login na aplicação usando email e senha.

```typescript
cy.login("<EMAIL>", "senha123");
```

### `cy.checkPageTitle(title)`

Verifica se o título da página contém o texto especificado.

```typescript
cy.checkPageTitle("Dashboard");
```

### `cy.waitForPageLoad()`

Aguarda o carregamento completo da página (aguarda elemento de loading desaparecer).

```typescript
cy.waitForPageLoad();
```

### `cy.interceptApiCalls()`

Intercepta todas as chamadas de API para monitoramento.

```typescript
cy.interceptApiCalls();
cy.wait("@apiGet");
```

## Convenções de Teste

### Data Attributes

Use `data-cy` attributes nos componentes para seleção nos testes:

```jsx
<button data-cy="submit-button">Enviar</button>
```

```typescript
cy.get("[data-cy=submit-button]").click();
```

### Estrutura de Testes

```typescript
describe("Nome do Grupo de Testes", () => {
  beforeEach(() => {
    // Configuração antes de cada teste
  });

  it("deve fazer algo específico", () => {
    // Implementação do teste
  });
});
```

## Fixtures

Use fixtures para dados de teste consistentes:

```typescript
cy.fixture("users").then((users) => {
  cy.login(users.validUser.email, users.validUser.password);
});
```

## Testes Responsivos

Os testes incluem verificações para diferentes viewports:

- Mobile: 375x667
- Tablet: 768x1024
- Desktop: 1280x720

## Interceptação de APIs

Configure interceptações para testar comportamentos de API:

```typescript
cy.intercept("GET", "/api/data", { fixture: "tracking-data" }).as("getData");
cy.visit("/dashboard");
cy.wait("@getData");
```

## Executando Testes

### Modo Desenvolvimento

```bash
npm run cypress:open
```

### Modo CI/CD

```bash
npm run cypress:run
```

### Com Servidor Local

```bash
npm run e2e
```

## Debugging

1. Use `cy.debug()` para pausar a execução
2. Use `cy.screenshot()` para capturar screenshots
3. Use o modo interativo para debug visual

## Melhores Práticas

1. **Evite sleeps fixos**: Use `cy.wait()` com aliases de API
2. **Use seletores estáveis**: Prefira `data-cy` attributes
3. **Mantenha testes independentes**: Cada teste deve poder rodar sozinho
4. **Use Page Objects**: Para testes complexos, considere o padrão Page Object
5. **Teste comportamentos, não implementação**: Foque no que o usuário faz

## Troubleshooting

### Problemas Comuns

1. **Timeout errors**: Aumente o timeout nas configurações
2. **Elementos não encontrados**: Verifique se os `data-cy` attributes estão corretos
3. **Falhas intermitentes**: Use `cy.wait()` adequadamente para aguardar APIs

### Configurações de Timeout

Ajuste no `cypress.config.ts`:

```typescript
defaultCommandTimeout: 10000,
requestTimeout: 10000,
responseTimeout: 10000,
```
