// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Lê dados de um arquivo de texto e retorna como array
       * @param filePath - Caminho para o arquivo de texto
       */
      readTestDataFile(filePath: string): Chainable<string[]>;

      /**
       * Realiza login na aplicação com os dados fornecidos
       * @param proposalCode - Código da proposta (numero-versao)
       * @param cpfCnpj - CPF ou CNPJ
       */
      loginWithData(proposalCode: string, cpfCnpj: string): Chainable<void>;

      /**
       * Realiza logout da aplicação
       */
      performLogout(): Chainable<void>;

      /**
       * Aguarda e faz scroll na página do dashboard
       */
      waitAndScroll(): Chainable<void>;
    }
  }
}

// Comando para ler arquivo de dados de teste
Cypress.Commands.add("readTestDataFile", (filePath: string) => {
  return cy.readFile(filePath, "utf8").then((content: string) => {
    return content
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
  });
});

// Comando para realizar login com dados específicos
Cypress.Commands.add(
  "loginWithData",
  (proposalCode: string, cpfCnpj: string) => {
    // Visita a página de acesso
    cy.visit("/access-page");

    // Aguarda a página carregar
    cy.get("form").should("be.visible");

    // Preenche o campo "Número do Pedido"
    cy.get('input[name="orderId"]').clear().type(proposalCode);

    // Preenche o campo "CPF/CNPJ"
    cy.get('input[name="cnpj"]').clear().type(cpfCnpj);

    // Clica no botão "Rastrear Pedido"
    cy.get('button[type="submit"]').contains("Rastrear Pedido").click();

    // Aguarda redirecionamento para dashboard (pode ser necessário ajustar a URL)
    cy.url().should("include", "/dashboard", { timeout: 10000 });
  },
);

// Comando para realizar logout
Cypress.Commands.add("performLogout", () => {
  // Tenta primeiro a versão desktop (botão visível)
  cy.get("body").then(($body) => {
    const logoutButton = $body.find(".logout-container:visible");

    if (logoutButton.length > 0) {
      // Desktop - clica diretamente no botão de logout
      cy.get(".logout-container").should("be.visible").click();
    } else {
      // Mobile - abre o menu hambúrguer primeiro
      cy.get('button[class*="text-3xl"]').first().click();
      cy.get(".logout-container").should("be.visible").click();
    }
  });

  // Aguarda redirecionamento para página de acesso
  cy.url().should("include", "/access-page", { timeout: 10000 });
});

// Comando para aguardar e fazer scroll
Cypress.Commands.add("waitAndScroll", () => {
  // Aguarda 2 segundos
  cy.wait(2000);

  // Faz scroll para baixo
  cy.scrollTo("bottom", { duration: 1000 });

  // Aguarda mais 2 segundos
  cy.wait(2000);
});

export {};
