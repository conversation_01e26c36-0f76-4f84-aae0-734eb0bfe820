# Teste de Integração - Fluxo Completo de Acesso

Este teste automatiza o fluxo completo de acesso ao siste<PERSON>, executando múltiplos logins e logouts baseados em dados de um arquivo de texto.

## Como Usar

### 1. Preparar os Dados de Teste

Edite o arquivo `cypress/fixtures/test-data.txt` e adicione seus dados de teste no formato:

```
numero-versao cpf/cnpj
```

**Exemplo:**
```
123456-1 12345678901
789012-2 98765432100
456789-3 11122233344
```

### 2. Executar o Teste

#### Modo Interativo (recomendado para desenvolvimento)
```bash
npm run cypress:open
```
Depois selecione o arquivo `integration-test.cy.ts`

#### Modo Headless (para CI/CD)
```bash
npm run cypress:run --spec "cypress/e2e/integration-test.cy.ts"
```

### 3. Fluxo do Teste

Para cada linha de dados, o teste executa:

1. **Acesso à página** `/access-page`
2. **Preenchimento dos campos:**
   - Campo "Número do Pedido" com `numero-versao`
   - Campo "CPF/CNPJ" com `cpf/cnpj`
3. **Clique no botão** "Rastrear Pedido"
4. **Aguarda 2 segundos** na página do dashboard
5. **Scroll para baixo** na página
6. **Aguarda mais 2 segundos**
7. **Clique no botão de logout** no header
8. **Retorna para a página de acesso**
9. **Repete para a próxima linha**

## Estrutura dos Arquivos

- `integration-test.cy.ts` - Teste principal
- `../fixtures/test-data.txt` - Seus dados de teste
- `../fixtures/test-data-example.txt` - Exemplo de formato
- `../support/commands.ts` - Comandos customizados

## Comandos Customizados Disponíveis

### `cy.readTestDataFile(filePath)`
Lê dados de um arquivo de texto e retorna como array.

### `cy.loginWithData(proposalCode, cpfCnpj)`
Realiza login com os dados fornecidos.

### `cy.performLogout()`
Realiza logout da aplicação (funciona em desktop e mobile).

### `cy.waitAndScroll()`
Aguarda 2 segundos, faz scroll para baixo e aguarda mais 2 segundos.

## Configurações

O teste está configurado para:
- **Viewport padrão:** 1280x720
- **Timeout:** 10 segundos para redirecionamentos
- **Interceptação de APIs:** Evita erros de rede durante testes

## Testes Adicionais Incluídos

### Teste Individual de Componentes
- Validação da página de acesso
- Validação de campos obrigatórios
- Validação do formato do número do pedido

### Teste de Responsividade
- Mobile (375x667)
- Tablet (768x1024)
- Desktop (1280x720)

## Solução de Problemas

### Erro: "Nenhum dado de teste encontrado"
- Verifique se o arquivo `test-data.txt` contém dados válidos
- Certifique-se de que as linhas não começam com `#`
- Verifique se há espaço entre `numero-versao` e `cpf/cnpj`

### Erro de timeout no login
- Verifique se a aplicação está rodando em `http://localhost:3000`
- Confirme se os dados de teste são válidos
- Verifique se não há problemas de rede

### Erro no logout
- O teste tenta detectar automaticamente se é desktop ou mobile
- Verifique se o botão de logout está visível na página

## Exemplo de Execução

```bash
# Inicia a aplicação
npm run dev

# Em outro terminal, executa o teste
npm run cypress:open
```

Ou para execução completa automatizada:

```bash
npm run e2e:open
```
