describe("Teste de Integração - Fluxo Completo de Acesso", () => {
  let testData: Array<{ proposalCode: string; cpfCnpj: string }> = [];

  before(() => {
    // Lê os dados do arquivo de teste com formato de aspas duplas
    cy.readQuotedTestDataFile("cypress/fixtures/test-data.txt").then((data) => {
      testData = data;

      if (testData.length === 0) {
        throw new Error(
          'Nenhum dado de teste encontrado. Adicione dados no arquivo cypress/fixtures/test-data.txt no formato "numero-versao""cpf/cnpj"',
        );
      }
    });
  });

  beforeEach(() => {
    // Configurações antes de cada teste
    cy.viewport(1280, 720);

    // Intercepta possíveis chamadas de API para evitar erros de rede
    cy.intercept("POST", "**/api/**", { statusCode: 200 }).as("apiCall");
  });

  it("Deve executar o fluxo completo para todos os dados de teste", () => {
    // Verifica se há dados para testar
    expect(testData.length).to.be.greaterThan(0);

    // Executa o teste para cada conjunto de dados
    testData.forEach((data, index) => {
      cy.log(
        `Executando teste ${index + 1}/${testData.length}: ${data.proposalCode} | ${data.cpfCnpj}`,
      );

      // Verifica se os dados são válidos
      if (!data.proposalCode || !data.cpfCnpj) {
        cy.log(`Dados inválidos ignorados: ${JSON.stringify(data)}`);
        return;
      }

      // Executa o fluxo de teste (sem exigir sucesso no login)
      executeTestFlowWithValidation(data.proposalCode, data.cpfCnpj, index + 1);
    });
  });

  it("Deve executar teste com dados de exemplo", () => {
    // Teste usando dados de exemplo para demonstração
    cy.readQuotedTestDataFile("cypress/fixtures/test-data-example.txt").then(
      (exampleData) => {
        if (exampleData.length > 0) {
          const data = exampleData[0];
          executeTestFlow(data.proposalCode, data.cpfCnpj, 1);
        }
      },
    );
  });

  /**
   * Executa o fluxo completo de teste para um conjunto de dados (versão original)
   */
  function executeTestFlow(
    proposalCode: string,
    cpfCnpj: string,
    testNumber: number,
  ) {
    cy.log(
      `Iniciando fluxo de teste ${testNumber} com dados: ${proposalCode} | ${cpfCnpj}`,
    );

    // Passo 1: Realizar login
    cy.loginWithData(proposalCode, cpfCnpj);

    // Passo 2: Aguardar e fazer scroll na página do dashboard
    cy.waitAndScroll();

    // Passo 3: Realizar logout
    cy.performLogout();

    // Aguarda um pouco antes do próximo teste
    cy.wait(100);
  }

  /**
   * Executa o fluxo de teste com validação flexível (não exige sucesso no login)
   */
  function executeTestFlowWithValidation(
    proposalCode: string,
    cpfCnpj: string,
    testNumber: number,
  ) {
    cy.log(
      `Iniciando fluxo de teste ${testNumber} com dados: ${proposalCode} | ${cpfCnpj}`,
    );

    // Passo 1: Tentar login - sempre continua independente do resultado
    cy.loginWithData(proposalCode, cpfCnpj).then(() => {
      // Passo 2: Verificar se chegou ao dashboard
      cy.url().then((currentUrl) => {
        if (currentUrl.includes("/dashboard")) {
          cy.log("✅ Login bem-sucedido - executando fluxo completo");

          // Aguardar e fazer scroll na página do dashboard
          cy.waitAndScroll();

          // Realizar logout
          cy.performLogout();
        } else {
          cy.log(
            "⚠️ Login não foi bem-sucedido - dados salvos para análise, prosseguindo...",
          );
        }
      });

      // Aguarda um pouco antes do próximo teste
      cy.wait(1000);

      // Log para confirmar que o teste foi finalizado
      cy.log(`🔚 Teste ${testNumber} finalizado - prosseguindo para próximo`);
    });
  }
});
