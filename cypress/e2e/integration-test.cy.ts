describe("Teste de Integração - Fluxo Completo de Acesso", () => {
  let testData: string[] = [];

  before(() => {
    // Lê os dados do arquivo de teste
    cy.readTestDataFile("cypress/fixtures/test-data.txt").then((data) => {
      console.log("Dados de teste:", data);
      // Filtra linhas que não são comentários
      testData = data.filter(
        (line: string) => !line.startsWith("#") && line.trim().length > 0,
      );

      if (testData.length === 0) {
        throw new Error(
          "Nenhum dado de teste encontrado. Adicione dados no arquivo cypress/fixtures/test-data.txt",
        );
      }
    });
  });

  beforeEach(() => {
    // Configurações antes de cada teste
    cy.viewport(1280, 720);

    // Intercepta possíveis chamadas de API para evitar erros de rede
    cy.intercept("POST", "**/api/**", { statusCode: 200 }).as("apiCall");
  });

  it("Deve executar o fluxo completo para todos os dados de teste", () => {
    // Verifica se há dados para testar
    expect(testData.length).to.be.greaterThan(0);

    // Executa o teste para cada linha de dados
    testData.forEach((line, index) => {
      cy.log(`Executando teste ${index + 1}/${testData.length}: ${line}`);

      // Separa os dados da linha (numero-versao e cpf/cnpj)
      const [proposalCode, cpfCnpj] = line
        .split(" ")
        .filter((item) => item.trim().length > 0);

      if (!proposalCode || !cpfCnpj) {
        cy.log(`Linha inválida ignorada: ${line}`);
        return;
      }

      // Executa o fluxo de teste
      executeTestFlow(proposalCode, cpfCnpj, index + 1);
    });
  });

  it("Deve executar teste com dados de exemplo", () => {
    // Teste usando dados de exemplo para demonstração
    cy.readTestDataFile("cypress/fixtures/test-data-example.txt").then(
      (exampleData) => {
        const validData = exampleData.filter(
          (line: string) => !line.startsWith("#") && line.trim().length > 0,
        );

        if (validData.length > 0) {
          const [proposalCode, cpfCnpj] = validData[0].split(" ");
          executeTestFlow(proposalCode, cpfCnpj, 1);
        }
      },
    );
  });

  /**
   * Executa o fluxo completo de teste para um conjunto de dados
   */
  function executeTestFlow(
    proposalCode: string,
    cpfCnpj: string,
    testNumber: number,
  ) {
    cy.log(
      `Iniciando fluxo de teste ${testNumber} com dados: ${proposalCode} | ${cpfCnpj}`,
    );

    // Passo 1: Realizar login
    cy.loginWithData(proposalCode, cpfCnpj);

    // Passo 2: Aguardar e fazer scroll na página do dashboard
    cy.waitAndScroll();

    // Passo 3: Realizar logout
    cy.performLogout();

    // Aguarda um pouco antes do próximo teste
    cy.wait(1000);
  }
});

// describe("Teste Individual de Componentes", () => {
//   beforeEach(() => {
//     cy.viewport(1280, 720);
//   });

//   it("Deve validar a página de acesso", () => {
//     cy.visit("/access-page");

//     // Verifica se os elementos principais estão presentes
//     cy.get("form").should("be.visible");
//     cy.get('input[name="orderId"]').should("be.visible");
//     cy.get('input[name="cnpj"]').should("be.visible");
//     cy.get('button[type="submit"]').should("contain", "Rastrear Pedido");
//   });

//   it("Deve validar campos obrigatórios", () => {
//     cy.visit("/access-page");

//     // Tenta submeter sem preencher campos
//     cy.get('button[type="submit"]').click();

//     // Verifica se permanece na mesma página (validação falhou)
//     cy.url().should("include", "/access-page");
//   });

//   it("Deve validar formato do número do pedido", () => {
//     cy.visit("/access-page");

//     // Preenche com formato inválido
//     cy.get('input[name="orderId"]').type("123456");
//     cy.get('input[name="cnpj"]').type("12345678901");
//     cy.get('button[type="submit"]').click();

//     // Verifica se mostra erro de validação
//     cy.contains("Digite um pedido válido").should("be.visible");
//   });
// });

// describe("Teste de Responsividade", () => {
//   const viewports = [
//     { name: "Mobile", width: 375, height: 667 },
//     { name: "Tablet", width: 768, height: 1024 },
//     { name: "Desktop", width: 1280, height: 720 },
//   ];

//   viewports.forEach((viewport) => {
//     it(`Deve funcionar corretamente em ${viewport.name}`, () => {
//       cy.viewport(viewport.width, viewport.height);
//       cy.visit("/access-page");

//       // Verifica se os elementos são visíveis no viewport
//       cy.get("form").should("be.visible");
//       cy.get('input[name="orderId"]').should("be.visible");
//       cy.get('input[name="cnpj"]').should("be.visible");
//       cy.get('button[type="submit"]').should("be.visible");
//     });
//   });
// });
