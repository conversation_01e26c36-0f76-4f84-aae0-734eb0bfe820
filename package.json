{"name": "pormade_tracker", "version": "1.3.3", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:component": "cypress run --component", "cypress:component:open": "cypress open --component", "e2e": "start-server-and-test dev http://localhost:3000 cypress:run", "e2e:open": "start-server-and-test dev http://localhost:3000 cypress:open", "test:e2e": "cypress run --spec 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}'", "test:component": "cypress run --component --spec 'cypress/component/**/*.cy.{js,jsx,ts,tsx}'"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.4.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "@tanstack/react-query": "^5.32.0", "@tanstack/react-query-devtools": "^5.32.0", "@types/google.maps": "^3.58.1", "@types/js-cookie": "^3.0.6", "@vis.gl/react-google-maps": "^1.4.2", "axios": "^1.7.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.0.8", "js-cookie": "^3.0.5", "lucide-react": "^0.473.0", "next": "^15.2.3", "react": "^19.0.0", "react-day-picker": "^9.5.1", "react-dom": "^19.0.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.1.0", "react-modal": "^3.16.1", "react-swipeable": "^7.0.1", "sharp": "^0.33.5", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@types/cypress": "^0.1.6", "@types/node": "^20.14.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@typescript-eslint/eslint-plugin": "^7.13.1", "cypress": "^14.4.1", "eslint": "^8.57.0", "eslint-config-next": "^15.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.2", "postcss": "^8", "prettier": "^3.2.5", "start-server-and-test": "^2.0.12", "tailwindcss": "^3.4.1", "typescript": "^5"}}