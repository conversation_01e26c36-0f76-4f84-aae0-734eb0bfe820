"use server";

import { getCookies } from "@/lib/cookies/saveCookies";
import { handleGlobalErrors } from "@/lib/handleErrors/handleRequestErros";
import { apiAdmin } from "@/services/api/apiInstance";
import { AUTH_ROUTES } from "../endpoints";

export const logoutRequest = async ({ clientId }: { clientId: string }) => {
  try {
    const token = await getCookies("tracker_admin_token");
    const { data, status } = await apiAdmin.post(
      AUTH_ROUTES.LOGOUT,
      {},
      {
        headers: {
          "X-Client-ID": clientId,
          Authorization: `Bearer ${token}`,
        },
      },
    );
    return { success: true, data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};
