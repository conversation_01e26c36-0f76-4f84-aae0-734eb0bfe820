"use server";

import { getCookies } from "@/lib/cookies/saveCookies";
import { handleGlobalErrors } from "@/lib/handleErrors/handleRequestErros";
import { apiAdmin } from "@/services/api/apiInstance";
import { IApiInstance } from "@/types/utils";
import { TRACKING_ROUTES } from "../endpoints";

interface IHashReturn {
  hash: string;
}
export const generateTrackingLinkRequest = async ({
  id,
  clientId,
}: {
  id: number;
  clientId: string;
}): Promise<IApiInstance<IHashReturn>> => {
  try {
    const token = await getCookies("tracker_admin_token");
    const { data, status } = await apiAdmin.post(
      TRACKING_ROUTES.GENERATE_TRACKING_LINK({ id }),
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "X-Client-ID": clientId,
        },
      },
    );
    return { success: true, data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};
