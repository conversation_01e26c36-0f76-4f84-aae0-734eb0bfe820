import { UseMutationResult } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { SetStateAction } from "react";
import { IResponse } from "../utils";

export type LoginVariables = { cpfCnpj: string; proposalCode: string };
export type LoginResponse = IResponse<unknown>;

export type ContextAccessProps = {
  login: (
    cpfCnpj: string,
    proposalCode: string,
    ip: string,
  ) => Promise<IResponse<unknown>>;
  isAuthenticated: boolean;
  setIsAuthenticated: React.Dispatch<SetStateAction<boolean>>;
  logout: () => void;
  hashExpired: boolean;
  setHashExpired: React.Dispatch<React.SetStateAction<boolean>>;
  fetchToDeliveries: boolean;
  setFetchToDeliveries: React.Dispatch<React.SetStateAction<boolean>>;
  accessMutation: UseMutationResult<LoginResponse, AxiosError, LoginVariables>;
  currentProductionSelected: string | null;
  setCurrentProductionSelected: React.Dispatch<
    React.SetStateAction<string | null>
  >;
  isNewProposal: boolean;
  setIsNewProposal: React.Dispatch<React.SetStateAction<boolean>>;
};

export type ContextAccessProviderProps = {
  children: React.ReactNode;
};
