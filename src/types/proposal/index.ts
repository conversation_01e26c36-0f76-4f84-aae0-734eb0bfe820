export interface Item {
  productType: string;
  amount: number;
  description: string;
}

export interface ProposalDataInterface {
  customer: string;
  email: string;
  proposal: string;
  status: string;
  freightValue: string;
  totalValue: string;
  carrier: string;
  deliveryAddress: Address;
  items: Item[];
  hasProduction: boolean;
  hasDelivery: boolean;
  approvedDate: string;
  deliveryForecastDate: string;
  newProposal: boolean;
  shipmentDate: string;
}

export interface ICoordinates {
  nome: string;
  latitude: number;
  longitude: number;
}

interface Address {
  street: string;
  number: string;
  complement: string;
  neighborhood: string;
  city: string;
  state: string;
  zipcode: string;
  lat: number;
  lng: number;
}
