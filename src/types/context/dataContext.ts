import { DeliveryInterface } from "../deliveries";
import { ProductionRequest } from "../production";
import { ProposalDataInterface } from "../proposal";

export type IDataContext = {
  proposalData: ProposalDataInterface | null | undefined;
  proposalLoading: boolean;
  proposalSuccess: boolean;
  proposalError: Error | null;
  proposalRefetch: () => void;
  productionData: ProductionRequest[] | null | undefined;
  productionLoading: boolean;
  productionSuccess: boolean;
  productionError: Error | null;
  productionRefetch: () => void;
  deliveriesData: DeliveryInterface[] | null | undefined;
  deliveriesLoading: boolean;
  deliveriesSuccess: boolean;
  deliveriesFetched: boolean;
  deliveriesError: Error | null;
  deliveriesRefetch: () => void;
  productionFetched: boolean;
  toastMessage: string | null;
  setToastMessage: React.Dispatch<React.SetStateAction<string | null>>;
  openNotificationHash: boolean;
  setOpenNotificationHash: React.Dispatch<React.SetStateAction<boolean>>;
};
