export type DetailsProductContextProps = {
  currentProductType: string | null;
  setCurrentProductType: React.Dispatch<React.SetStateAction<string | null>>;
  scrollEventsRef: React.RefObject<HTMLDivElement>;
  changeProductTypeButton: string | null;
  setChangeProductTypeButton: React.Dispatch<
    React.SetStateAction<string | null>
  >;
  setAnimationKeyDelivery: React.Dispatch<React.SetStateAction<number>>;
  setAnimationKeyProduction: React.Dispatch<React.SetStateAction<number>>;
  animationKeyDelivery: number;
  animationKeyProduction: number;
  isWhatsappOpen: boolean;
  setIsWhatsappOpen: React.Dispatch<React.SetStateAction<boolean>>;
};
