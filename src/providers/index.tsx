"use client";

import { AcessProvider } from "@/contexts/acess/acessContext";
import { DataProvider } from "@/contexts/data/dataContext";
import { DetailsProductProvider } from "@/contexts/detailsProduct/detailsProductContext";
import { ThemeProvider } from "@/contexts/theme/themeContext";
import {
  HydrationBoundary,
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query";
import { APIProvider } from "@vis.gl/react-google-maps";
import React, { useState } from "react";

export type ProvidersProps = {
  readonly children: React.ReactNode;
};

const dehydratedState = {
  queries: [],
};

const config = {
  googleMapsApiKey: process.env.NEXT_PUBLIC_MAPS_API_KEY,
  allowedReferer: "http://localhost:3000",
};

export default function Providers({ children }: ProvidersProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
            refetchOnReconnect: false,
            refetchOnMount: false,
          },
        },
      }),
  );

  return (
    <QueryClientProvider client={queryClient}>
      <HydrationBoundary state={dehydratedState}>
        <AcessProvider>
          <DataProvider>
            <DetailsProductProvider>
              <APIProvider apiKey={config.googleMapsApiKey ?? ""}>
                <ThemeProvider>{children}</ThemeProvider>
              </APIProvider>
            </DetailsProductProvider>
          </DataProvider>
        </AcessProvider>
      </HydrationBoundary>
    </QueryClientProvider>
  );
}
