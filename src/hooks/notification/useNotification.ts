import { useDataContext } from "@/contexts/data/dataContext";
import fetchEmail, {
  IEmail,
} from "@/hooks/notification/functions/fetchRequest";
import updateEmail, {
  IUpdate,
} from "@/hooks/notification/functions/updateEmail";
import { ErrorData } from "@/lib/handleErrors/handleRequestErros";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import {
  useMutation,
  UseMutationResult,
  useQuery,
} from "@tanstack/react-query";
import { AxiosError } from "axios";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";

const useNotification = (
  setOpen: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  const notificationElement = document.getElementById("notification-config");

  const { setOpenNotificationHash } = useDataContext();
  const [emailNotification, setEmailNotification] = useState(false);
  const modalRef = React.useRef<HTMLDivElement>(null);

  const {
    data: dataEmail,
    isLoading: loadingEmail,
    refetch,
    error,
  } = useQuery<IEmail | undefined | null>({
    enabled: true,
    queryKey: ["email"],
    queryFn: async () => {
      const data = await fechData();
      return data;
    },
  });

  useEffect(() => {
    if (!notificationElement) {
      const toastRoot = document.createElement("div");
      document.body.appendChild(toastRoot);
    }
  }, [notificationElement]);

  const fechData = async () => {
    const id = await getFingerprint();
    const data = await fetchEmail(id);
    if (data.success) {
      return data.data as IEmail;
    } else {
      return null;
    }
  };

  const updateMutation: UseMutationResult<IUpdate | ErrorData, AxiosError> =
    useMutation({
      mutationKey: ["email"],
      mutationFn: async (): Promise<IUpdate | ErrorData> => {
        const id = await getFingerprint();
        const response = await updateEmail(emailNotification, id);

        return response.data;
      },
      onSuccess: (response: IUpdate) => {
        if (response.message === "Alterado com sucesso.") {
          refetch();
          setOpen(false);
          toast.dismiss();
          toast.success("Configurações atualizadas com sucesso");
        } else {
          refetch();
          setOpen(false);
          toast.dismiss();
          toast.error("Ocorreu um erro ao atualizar as configurações");
        }
      },
    });

  const updateSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (emailNotification === dataEmail?.email) {
      return;
    }
    updateMutation.mutate({
      email: emailNotification,
    });
  };

  // const updateWhatsappMutation: UseMutationResult<
  //   IUpdate | ErrorData,
  //   AxiosError
  // > = useMutation({
  //   mutationKey: ["whatsapp"],
  //   mutationFn: async (): Promise<IUpdate | ErrorData> => {
  //     const id = await getFingerprint();
  //     const response = await updateEmail(emailNotification, id);
  //     return response.data;
  //   },
  // });

  const handleClickOutside = (event: React.MouseEvent) => {
    setOpenNotificationHash(false);
    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
      setOpen(false);
    }
  };

  useEffect(() => {
    if (dataEmail?.email) {
      setEmailNotification(dataEmail.email);
    }
  }, [dataEmail]);

  return {
    notificationElement,
    modalRef,
    emailNotification,
    setEmailNotification,
    updateSubmit,
    handleClickOutside,
    loadingEmail,
    dataEmail,
    error,
  };
};

export default useNotification;
