"use server";

import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";
import apiInstance from "@/services/api/apiInstance";
import { IResponse } from "@/types/utils";

export interface IUpdate {
  message: string;
}

const updateEmail = async (
  email: boolean,
  id: string,
): Promise<IResponse<IUpdate | ErrorData>> => {
  try {
    const { status, data } = await apiInstance.patch<IUpdate>(
      "/notification-options/update",
      {
        email,
      },
      {
        headers: {
          "X-Client-ID": id,
        },
      },
    );

    return { success: true, data, status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};

export default updateEmail;
