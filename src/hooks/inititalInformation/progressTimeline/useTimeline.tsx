"use client";

import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";
import { useCurrentProduction } from "@/hooks/productionProgress/useCurrentProduction";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { AiOutlineLike } from "react-icons/ai";
import { BsDoorOpen } from "react-icons/bs";
import { FaCogs, FaDolly } from "react-icons/fa";
import { LuClipboardCheck } from "react-icons/lu";
import { MdOutlineCheckCircle, MdOutlineLocalShipping } from "react-icons/md";
import { useNewSgnCase } from "../sgn/sgnCase";

const useTimeline = () => {
  const { stage, status, dhAprovado } = useInitialInformation();
  const { productionData, deliveriesData, proposalData } = useDataContext();
  const { currentProductionSelected } = useAcessContext();
  const { hasProduction: temProducao, hasDelivery: temEntrega } =
    proposalData || {};
  const newSgnCaseStages = useNewSgnCase();
  const [progress, setProgress] = useState<number>(0);
  const [activeStages, setActiveStages] = useState<Record<string, boolean>>({});
  const [isAnimationActive, setIsAnimationActive] = useState(false);
  const [currentStageActive, setCurrentStageActive] = useState<string | null>(
    null,
  );
  const [initialLoadDone, setInitialLoadDone] = useState(false);
  const currentIndex = useRef<number | undefined>(undefined);
  const oldIndex = useRef<number | undefined>(undefined);
  const { getCurrentDelivery } = useActiveDelivery();
  const { getCurrentProduction } = useCurrentProduction();

  const defineStage = useCallback(
    (current: string) => {
      if (current === "Expedição" && !temProducao && temEntrega)
        return "Expedição";
      if (current === "Expedição" && temProducao && !temEntrega)
        return "Finalizado";
      if (current === "Expedição" && !temProducao && !temEntrega)
        return "Finalizado";
      if (current === "Manufaturado" && !temEntrega) return "Finalizado";
      return current;
    },
    [temProducao, temEntrega],
  );

  const stages = useMemo(() => {
    if (!proposalData) return null;
    if (temProducao && temEntrega)
      return [
        "Aprovado",
        "Em Produção",
        "Manufaturado",
        "Expedição",
        "Em Trânsito",
        "Entregue",
      ];
    if (temEntrega && !temProducao)
      return ["Aprovado", "Expedição", "Em Trânsito", "Entregue"];
    if (!temEntrega && temProducao)
      return ["Aprovado", "Em Produção", "Manufaturado", "Finalizado"];
    return ["Aprovado", "Finalizado"];
  }, [temProducao, temEntrega, proposalData]);

  const setTimeCssAnimation = ({
    transition,
    time,
  }: {
    transition: number;
    time: number;
  }) => {
    document.documentElement.style.setProperty("--animationTime", `${time}ms`);
    document.documentElement.style.setProperty(
      "--transitionTime",
      `${transition}ms`,
    );
  };

  const calculateStage = useCallback((): {
    transition: number;
    time: number;
  } => {
    if (!stages) {
      return { time: 0, transition: 0 };
    }
    const timePerStage =
      stages.length >= 5 ? 600 : stages.length >= 3 ? 600 : 900;
    const transition = timePerStage;

    const stageDifference = currentIndex.current! - oldIndex.current!;
    const time =
      stageDifference !== 0 ? transition * Math.abs(stageDifference) : 0;
    return {
      transition,
      time,
    };
  }, [stages]);

  const animateStages = useCallback(
    (oldIndex: number, currentIndex: number, totalTime: number) => {
      if (!stages) return;
      const stepsCount = Math.abs(oldIndex - currentIndex);
      if (stepsCount === 0) return;
      const interval = totalTime / stepsCount;
      if (oldIndex > currentIndex) {
        for (let i = oldIndex; i > currentIndex; i--) {
          setTimeout(
            () => {
              setActiveStages((prev) => ({ ...prev, [stages[i]]: false }));
            },
            (oldIndex - i) * interval,
          );
        }
      } else {
        for (let i = oldIndex + 1; i <= currentIndex; i++) {
          setTimeout(
            () => {
              setActiveStages((prev) => ({ ...prev, [stages[i]]: true }));
            },
            currentIndex === 0 ? 0 : (i - oldIndex) * interval,
          );
        }
      }
    },
    [stages, setActiveStages],
  );

  useEffect(() => {
    if (!stage || !stages || !proposalData) return;
    const stageName = defineStage(stage);

    currentIndex.current = stages.indexOf(stageName);

    if (!initialLoadDone || currentStageActive === null) {
      oldIndex.current = -1;
    } else {
      oldIndex.current = stages.indexOf(currentStageActive);
    }

    const { transition, time } = calculateStage();

    if (time > 0) {
      setTimeCssAnimation({ transition, time });
      animateStages(oldIndex.current, currentIndex.current, transition);

      setTimeout(() => {
        setIsAnimationActive(true);
        let newProgress =
          ((currentIndex.current ?? 0) / (stages.length - 1)) * 100;
        document.documentElement.style.setProperty("--bar", `${0}%`);
        if (currentIndex.current ?? 0 < stages.length - 1) newProgress += 5;
        setProgress(newProgress);
        document.documentElement.style.setProperty("--bar", `${newProgress}%`);
      }, 100);
    }

    setInitialLoadDone(true);
    setCurrentStageActive(stageName);
  }, [
    stage,
    initialLoadDone,
    stages,
    proposalData,
    currentStageActive,
    defineStage,
    animateStages,
    calculateStage,
    newSgnCaseStages,
  ]);

  const isActive = (current: string) => Boolean(activeStages[current]);

  const isActiveCicle = (current: string) =>
    isActive(current)
      ? "text-PormadeGreen bg-bgGreenTimeline border-green-800 animationActive"
      : "text-circleProgressTimeline bg-circleProgress";

  const isActiveText = (current: string) =>
    isActive(current)
      ? "text-bgGreenTimeline font-bold"
      : "font-bold text-textStageInactive";

  const isActiveIcon = (current: string) =>
    isActive(current)
      ? "text-white bg-bgGreenTimeline animationActive"
      : "text-circleProgressTimeline";

  const handleDateStage = (current: string) =>
    isActive(current)
      ? "text-bgGreenTimeline font-bold transitionLinear"
      : "text-textInactiveProgress hidden";

  const delivery = getCurrentDelivery({
    delivery: deliveriesData,
    isNew: proposalData?.newProposal,
  });

  const inTransitDate = useCallback(() => {
    if (!delivery?.delivery?.collectionDate) return proposalData?.shipmentDate;
    return delivery?.delivery?.collectionDate;
  }, [proposalData, delivery]);

  const deliveredDate = useCallback(
    () => delivery?.delivery?.deliveryDate ?? "",
    [delivery],
  );

  const production = getCurrentProduction({
    production: productionData,
    isNew: proposalData?.newProposal,
  });

  const dates = useMemo(
    () => ({
      Aprovado: dhAprovado ?? "",
      "Em Produção": production?.startProductionDate ?? "",
      Manufaturado: production?.manufacturedDate ?? "",
      Expedição: proposalData?.newProposal
        ? (production?.awaitingCollectionDate ?? "")
        : (proposalData?.shipmentDate ?? ""),
      "Em Trânsito": inTransitDate() ?? "",
      Entregue: deliveredDate() ?? "",
      Finalizado: production?.manufacturedDate
        ? production?.manufacturedDate
        : (proposalData?.shipmentDate ?? ""),
    }),
    [dhAprovado, proposalData, inTransitDate, deliveredDate, production],
  );

  const ComponentsCircle = useMemo(() => {
    if (!proposalData) return [];
    if (temEntrega && temProducao) {
      return [
        { stage: "Aprovado", Icon: MdOutlineCheckCircle, name: "Aprovado" },
        { stage: "Em Produção", Icon: FaCogs, name: "Em Produção" },
        { stage: "Manufaturado", Icon: FaDolly, name: "Manufaturado" },
        { stage: "Expedição", Icon: LuClipboardCheck, name: "Expedido" },
        {
          stage: "Em Trânsito",
          Icon: MdOutlineLocalShipping,
          name: "Em Trânsito",
        },
        { stage: "Entregue", Icon: BsDoorOpen, name: "Entregue" },
      ];
    }
    if (temEntrega && !temProducao) {
      return [
        { stage: "Aprovado", Icon: MdOutlineCheckCircle, name: "Aprovado" },
        { stage: "Expedição", Icon: LuClipboardCheck, name: "Expedido" },
        {
          stage: "Em Trânsito",
          Icon: MdOutlineLocalShipping,
          name: "Em Trânsito",
        },
        { stage: "Entregue", Icon: BsDoorOpen, name: "Entregue" },
      ];
    }
    if (!temEntrega && temProducao) {
      return [
        { stage: "Aprovado", Icon: MdOutlineCheckCircle, name: "Aprovado" },
        { stage: "Em Produção", Icon: FaCogs, name: "Em Produção" },
        { stage: "Manufaturado", Icon: FaDolly, name: "Manufaturado" },
        { stage: "Finalizado", Icon: AiOutlineLike, name: "Finalizado" },
      ];
    }
    return [
      { stage: "Aprovado", Icon: MdOutlineCheckCircle, name: "Aprovado" },
      { stage: "Finalizado", Icon: AiOutlineLike, name: "Finalizado" },
    ];
  }, [temProducao, temEntrega, proposalData]);

  return {
    stages,
    status,
    progress,
    activeStages,
    isAnimationActive,
    isActive,
    isActiveCicle,
    isActiveIcon,
    isActiveText,
    handleDateStage,
    dates,
    ComponentsCircle,
  };
};

export default useTimeline;
