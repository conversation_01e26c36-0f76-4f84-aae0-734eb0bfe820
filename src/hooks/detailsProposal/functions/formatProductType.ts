"use client";

export const formatProductType = (tipo: string | null): string | undefined => {
  if (!tipo) return;

  const lastWord = tipo.trim().split(/\s+/).pop();
  switch ((lastWord ?? "").toUpperCase()) {
    case "PORTA PRONTA ACM":
      return "Porta Pronta ACM";
    case "PORTA PRONTA":
      return "Porta Pronta";
    case "PORTA":
      return "Porta";
    case "GUARNICAO":
      return "Guarnição";
    case "BATENTE":
      return "Batente";
    case "FECHADURA":
      return "Fechadura";
    case "ACESSORIOS":
      return "Acessórios";
    case "DOBRADICA":
      return "Do<PERSON>di<PERSON>";
    case "SERVICOS":
      return "Serviços";
    case "BANDEIRA":
      return "Bandeira";
    case "RODAPE":
      return "Rodapé";
    default:
      return tipo.trim();
  }
};
