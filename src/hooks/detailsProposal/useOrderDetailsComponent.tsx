"use client";
import { useDataContext } from "@/contexts/data/dataContext";

import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import { Item } from "@/types/proposal";
import { useEffect, useMemo, useRef, useState } from "react";
import { calculateTotalValue } from "./functions/calculateQuantityValue";
import { groupedDataDetailsTable } from "./functions/groupDataDetailsTable";

export const useOrderDetailsComponent = () => {
  const { proposalData } = useDataContext();
  const { setCurrentProductType, currentProductType } =
    useDetailsProductContext();
  const [currentPageByType, setCurrentPageByType] = useState<
    Record<string, number>
  >({});
  const [totalQuantityValue, setTotalQuantityValue] = useState<number | null>(
    null,
  );
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [hasScroll, setHasScroll] = useState(false);
  const [groupedItens, setGroupedItens] = useState<{ [key: string]: Item[] }>(
    {},
  );

  useEffect(() => {
    if (proposalData) {
      const fetchData = async () => {
        const result = await groupedDataDetailsTable(proposalData);
        setGroupedItens(result.groupedData);
        setCurrentProductType(result.inititalType);
      };
      fetchData();
    }
  }, [proposalData, setCurrentProductType]);

  const groupedComponents = useMemo(() => groupedItens, [groupedItens]);

  useEffect(() => {
    if (!proposalData || !currentProductType) return;
    const calculate = async () => {
      const totalQuantity = await calculateTotalValue(
        groupedComponents,
        currentProductType,
      );
      setTotalQuantityValue(totalQuantity);
    };
    calculate();
  }, [currentProductType, currentPageByType, groupedComponents, proposalData]);

  const currentPage = currentPageByType[currentProductType ?? ""] || 1;
  const componentsOfType = useMemo(() => {
    const components = groupedComponents[currentProductType ?? ""] || [];
    return components;
  }, [currentProductType, groupedComponents]);

  const filteredComponents = useMemo(() => {
    return componentsOfType.slice((currentPage - 1) * 1, currentPage * 1);
  }, [componentsOfType, currentPage]);

  const formatDescription = (description: string) => {
    if (description?.includes("PROMOCAO")) {
      return description.replace("PROMOCAO -", "").trim();
    } else {
      return description;
    }
  };

  const groupedItensOfDesc = useMemo(() => {
    return componentsOfType.reduce(
      (
        acc: { [key: string]: { description: string; amount: number } },
        { description, amount },
      ) => {
        const item = acc[description] || { description, amount: 0 };
        item.amount += amount;
        acc[description] = item;
        return acc;
      },
      {},
    );
  }, [componentsOfType]);

  const componentsToRender = useMemo(
    () => Object.values(groupedItensOfDesc).sort((a, b) => b.amount - a.amount),
    [groupedItensOfDesc],
  );

  const checkForScroll = () => {
    const element = scrollContainerRef.current;
    if (!element) return;
    const hasOverflow = element.scrollHeight > element.clientHeight;
    setHasScroll(hasOverflow);
  };

  useEffect(() => {
    checkForScroll();
    window.addEventListener("resize", checkForScroll);
    return () => window.removeEventListener("resize", checkForScroll);
  }, [proposalData, componentsOfType]);

  return {
    currentPageByType,
    setCurrentPageByType,
    currentProductType,
    setCurrentProductType,
    groupedComponents,
    filteredComponents,
    totalQuantityValue,
    setTotalQuantityValue,
    componentsOfType,
    scrollContainerRef,
    hasScroll,
    setHasScroll,
    componentsToRender,
    formatDescription,
  };
};
