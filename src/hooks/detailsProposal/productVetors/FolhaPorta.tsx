export const FolhaPorta = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="290"
    height="595"
    viewBox="0 0 290 595"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="1"
      y="1"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="255"
      y="292.338"
      width="12"
      height="13.6616"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M256 294.026C256 293.75 256.224 293.526 256.5 293.526H265.5C265.776 293.526 266 293.75 266 294.026V304.906C266 305.182 265.776 305.406 265.5 305.406H256.5C256.224 305.406 256 305.182 256 304.906V294.026Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter0_i_605_150)">
      <path
        d="M256 275C256 272.791 257.791 271 260 271H262C264.209 271 266 272.791 266 275V276C266 278.209 264.209 280 262 280H260C257.791 280 256 278.209 256 276V275Z"
        fill="#474747"
      />
    </g>
    <rect
      x="255"
      y="267"
      width="12"
      height="18"
      fill="#010101"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter1_d_605_150)">
      <path
        d="M224 272H262C264.209 272 266 273.791 266 276C266 278.209 264.209 280 262 280H224V272Z"
        fill="#222121"
      />
      <path
        d="M224.5 272.5H262C263.933 272.5 265.5 274.067 265.5 276C265.5 277.933 263.933 279.5 262 279.5H224.5V272.5Z"
        stroke="white"
        strokeOpacity="0.4"
      />
    </g>
    <defs>
      <filter
        id="filter0_i_605_150"
        x="256"
        y="271"
        width="10"
        height="13"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_605_150"
        />
      </filter>
      <filter
        id="filter1_d_605_150"
        x="220"
        y="272"
        width="50"
        height="16"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_605_150"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_605_150"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
