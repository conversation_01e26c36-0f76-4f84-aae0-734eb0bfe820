export const Biombo = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="741"
    height="730"
    viewBox="0 0 741 730"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
  >
    <g filter="url(#filter0_d_643_151)">
      <path
        d="M9 5L183.105 43.6124L296.074 5L456.888 32.9607L620.36 5L732 43.6124L725.355 677.388L620.36 716L456.888 692.034L296.074 716L183.105 677.388L24.9485 716L9 5Z"
        fill="url(#pattern0_643_151)"
        shapeRendering="crispEdges"
      />
      <path
        d="M183.105 43.6124L9 5L24.9485 716L183.105 677.388M183.105 43.6124L296.074 5M183.105 43.6124V677.388M296.074 5L456.888 32.9607M296.074 5V716M456.888 32.9607L620.36 5M456.888 32.9607V692.034M620.36 5L732 43.6124L725.355 677.388L620.36 716M620.36 5V716M620.36 716L456.888 692.034M456.888 692.034L296.074 716M296.074 716L183.105 677.388"
        stroke="black"
        strokeWidth="8"
        shapeRendering="crispEdges"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_643_151"
        x="0.886719"
        y="-0.00927734"
        width="739.143"
        height="729.076"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_643_151"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_643_151"
          result="shape"
        />
      </filter>
      <pattern
        id="pattern0_643_151"
        patternContentUnits="objectBoundingBox"
        width="0.2213"
        height="0.225035"
      >
        <use
          xlinkHref="#image0_643_151"
          transform="scale(0.00138313 0.00140647)"
        />
      </pattern>
      <image
        id="image0_643_151"
        width="160"
        height="160"
        xlinkHref="data:image/png;base64,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"
      />
    </defs>
  </svg>
);
