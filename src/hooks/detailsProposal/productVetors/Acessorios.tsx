export const Acessorios = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="570"
    height="640"
    viewBox="0 0 570 640"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_d_678_152)">
      <g filter="url(#filter1_di_678_152)">
        <path
          d="M484 342L269.5 393.5L309.5 524L523.5 463C531.333 456.833 544.7 435 535.5 397C526.3 359 497.333 344.5 484 342Z"
          fill="#D9D9D9"
        />
        <path
          d="M484 342L269.5 393.5L309.5 524L523.5 463C531.333 456.833 544.7 435 535.5 397C526.3 359 497.333 344.5 484 342Z"
          stroke="black"
          strokeWidth="3"
        />
      </g>
      <g filter="url(#filter2_i_678_152)">
        <path
          d="M307 529C299.8 559.4 277 578.333 266.5 584L259 586L247.5 588H236.5L226.5 587L213 583L200 577L181.5 566.5L170 557L159.5 547.5L149.5 536.5L139.5 521.5L134.5 509.5L127.5 485.5V460L131.5 437L138 419L147 403.5L162 383L174 372L189 363.5L206.5 360.5L225 362C230.5 363.667 244.5 369.3 256.5 378.5C271.5 390 292.5 430 299.5 447C306.5 464 316 491 307 529Z"
          fill="#D9D9D9"
        />
      </g>
      <path
        d="M307 529C299.8 559.4 277 578.333 266.5 584L259 586L247.5 588H236.5L226.5 587L213 583L200 577L181.5 566.5L170 557L159.5 547.5L149.5 536.5L139.5 521.5L134.5 509.5L127.5 485.5V460L131.5 437L138 419L147 403.5L162 383L174 372L189 363.5L206.5 360.5L225 362C230.5 363.667 244.5 369.3 256.5 378.5C271.5 390 292.5 430 299.5 447C306.5 464 316 491 307 529Z"
        stroke="black"
        strokeWidth="3"
      />
      <path
        d="M199 516L175.5 536L144 507L141 461.5L165.5 443.5L195 471.5L199 516Z"
        stroke="black"
        strokeWidth="3"
      />
      <g filter="url(#filter3_d_678_152)">
        <path
          d="M182.5 499C187.333 503.667 197.3 513.4 198.5 515L194.5 471.5L180 458L179.5 458.5L182.5 499Z"
          fill="#555555"
        />
        <path
          d="M182.5 499C187.333 503.667 197.3 513.4 198.5 515L194.5 471.5L180 458L179.5 458.5L182.5 499Z"
          stroke="black"
          strokeWidth="3"
        />
      </g>
      <g filter="url(#filter4_i_678_152)">
        <path
          d="M176 455L140.5 462.5L145 505.5C145.333 506 146.1 507.1 146.5 507.5C146.9 507.9 170 501.333 181.5 498L179.5 459L176 455Z"
          fill="#D9D9D9"
        />
      </g>
      <path
        d="M176 455L140.5 462.5L145 505.5C145.333 506 146.1 507.1 146.5 507.5C146.9 507.9 170 501.333 181.5 498L179.5 459L176 455Z"
        stroke="black"
        strokeWidth="3"
      />
      <path
        d="M165.5 443L142 461.5L176 454.5L165.5 443Z"
        fill="#D9D9D9"
        stroke="black"
        strokeWidth="3"
      />
      <g filter="url(#filter5_i_678_152)">
        <path
          d="M182.5 499L146 509L175.5 535.5L199.5 515L182.5 499Z"
          fill="#D9D9D9"
        />
      </g>
      <path
        d="M182.5 499L146 509L175.5 535.5L199.5 515L182.5 499Z"
        stroke="black"
        strokeWidth="3"
      />
      <path
        d="M313.5 474.5L312.5 491.5L309.5 479.5L307.5 464L304 450L297 431L286.5 411L293 417.5C294.833 419.667 298.6 424.2 299 425C299.5 426 306.5 438.5 306.5 439C306.5 439.5 312.5 458.5 312.5 459.5C312.5 460.3 313.167 469.833 313.5 474.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M320.5 501C318.667 504.833 315 512.6 315 513L318.5 510.5L324 502.5L326.5 496L329 488L330 477L329.5 457.5L326.5 445L320 428L315 420.5L309.5 413L304 408L300.5 405.5L294.5 401L289 398L284 397.5L277.5 398L283.5 400.5L292 406.5L299 414L306.5 423C308.167 425.833 311.6 431.7 312 432.5C312.4 433.3 315.833 441.5 317.5 445.5L320.5 456.5L323 469.5V490.5L320.5 501Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M341 497L332.5 508L335 501.5L338 491L340 477C340.167 475.333 340.4 471.9 340 471.5C339.6 471.1 339.5 465.667 339.5 463L336.5 449L334 441L327.5 426L321.5 416.5L312 405.5L302.5 397.5L295 393.5H299L303 394.5L310 396L314.5 398L321.5 404L330 413.5L337.5 426L341.5 437L345 454L346 474.5L345 485L341 497Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M354 497.5L348 504L351 497.5L354 488.5L356.5 472.5L356 462L354 449.5L349.5 434L340.5 416.5L328 401L321 395L311.5 389.5L322.5 390.5L330.5 394L339 400.5C341.5 403.5 346.6 409.6 347 410C347.4 410.4 352.5 419.5 355 424L359.5 437.5L362 452L362.5 467L361.5 477L358.5 487.5L354 497.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M369.5 487.5L363.5 500L369 494.5L372 489.5L377 476L378.5 462L377 438.5L373 425.5L367.5 414.5L359 401L352.5 394L343.5 388.5L332 385.5L327.5 386.5L332 388L339.5 392.5L349 402.5L358.5 414.5L367.5 435L371 449L372.5 457.5L371.5 477.5L369.5 487.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M385 485L380 495L383.5 492L387.5 487L393 472.5L394.5 463.5V446.5L392.5 435.5L389 422L383 408.5L378.5 401.5L371.5 393.5L364.5 387L356 383L344 381.5L348.5 383.5L352 386.5L357 390L367 400L373.5 410L380 422L384 432.5L387.5 450.5L388.5 464L387 477L385 485Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M400.5 480L396.5 490L403.5 482.5L406.5 474.5L410 463V444.5L408 427.5L402.5 411.5L395 398L384 386C381.167 384 375.4 380 375 380C374.6 380 369.167 378.667 366.5 378H360.5L368 382L372.5 385L378.5 391L384.5 398.5L391 408.5L394 415L397.5 423.5L401.5 435L403.5 446.5L403 469.5L400.5 480Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M416 476.5L411.5 486.5L416 482.5L419.5 477L423 469L425 459L426 445.5L425 433.5L420 414.5L416.5 405L410 393.5L401.5 383.5L392 376L383.5 374H377L378 375L383 378L387.5 381L394 387L402 396.5L410 410.5L417.5 432.5L419 442.5V466L416 476.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M433 468.5L427.5 482L429 481L432 476.5L436 470L439.5 460.5L441 449.5V431.5L437.5 414L433 402.5L427 391L419 381L409 373L401.5 370H392.5L397 373L401.5 375L407.5 380.5C409.167 382.5 412.6 386.6 413 387C413.4 387.4 416.5 390.833 418 392.5L421.5 399L427 409.5L431 419.5L434 433L435 445.5V458L433 468.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M448 466L443 477.5L450 468.5L452.5 463.5L456.5 448.5L455 422L453.5 415.5L451 406L449 400.5L446.5 395.5L444 391L441 385.5L436 379.5L432 375L424.5 370L419.5 367.5L414 366L408.5 366.5L412 368.5L416.5 371L424 377.5C427.167 381.5 433.6 389.6 434 390C434.4 390.4 439.5 399.833 442 404.5L446 414.5L449 426.5L450.5 439L450 450.5L449.5 457L448 466Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M462.5 463.5C461 466.5 458 472.8 458 474L462.5 470L465.5 464L468 459.5L469.5 454L471.5 445.5L471 420.5L469 411.5L465.5 399L462.5 392L452.5 376.5L445.5 369.5L432 362.5H424L433 368L440.5 374.5L447 382.5L454.5 394.5L459.5 406.5L464.5 423.5L465.5 431V452.5L462.5 463.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M479.5 453.5L473 469L477.5 465L481 459L486 444L487 428L485 411.5L481.5 397L475.5 384L470 376L465 369L461 365.5L451.5 360.5L444 358L439.5 359.5L443 360.5L448.5 364L456.5 371C458.5 373.5 462.6 378.6 463 379C463.4 379.4 467.833 387.167 470 391L478 412.5L480 421L480.5 435.5V445L479.5 453.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M494 451C494 451.8 490.333 460.667 488.5 465L495.5 457L498.5 449.5L501 439L502 427.5L501 410.5L498.5 401.5L493 385L485.5 372.5L473.5 360L465 355.5L458 354.5L455 355.5L459.5 357.5L466.5 363L474 369.5L480 378C481.5 380.667 484.5 386.1 484.5 386.5C484.5 387 488.5 395.5 489 396.5C489.5 397.5 493 408 493 409C493 409.8 494.667 417 495.5 420.5L496 439C495.333 442.667 494 450.2 494 451Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M483 353L470.5 351.5L484 361.5L488.5 367.5L494 374.5L500 384.5L507 401.5C507.833 405.333 509.6 413.1 510 413.5C510.4 413.9 511.167 424 511.5 429L510 441.5L507 452L503 461.5L505.5 460L509.5 454.5L513.5 445.5L516 435L516.5 424.5L516 409.5L512.5 392L506 377.5L494 360.5L483 353Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M495.5 349L484 347.5L490 350.5L494 353.5L500 359L507.5 367.5L514.5 379.5L521 394.5L524.5 411V417L525 420.5V429.5L524.5 438C523.833 441 522.5 447.1 522.5 447.5C522.5 447.9 518.833 455 517 458.5L520.5 455.5L526.5 446L530 434L531.5 420.5C530.833 413.167 529.4 398.3 529 397.5C528.6 396.7 527.167 391.5 526.5 389L520.5 375L512.5 362.5L504.5 354L495.5 349Z"
        fill="white"
        stroke="black"
      />
    </g>
    <g filter="url(#filter6_i_678_152)">
      <path
        d="M228.5 209L217.5 158.5L38.5 123.5L14.5 147L38.5 234.5L146.5 264.5L228.5 209Z"
        fill="#D9D9D9"
      />
    </g>
    <path
      d="M228.5 209L217.5 158.5L38.5 123.5L14.5 147L38.5 234.5L146.5 264.5L228.5 209Z"
      stroke="black"
      strokeWidth="3"
    />
    <path
      d="M145.5 264.5L135.5 323.5C138.7 330.7 190.833 291.167 216.5 270.5C221 251.833 229.4 213.8 227 211C224.6 208.2 171.667 245.5 145.5 264.5Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="3"
    />
    <path
      d="M38.5 298L38.6422 298.086C50.0144 299.616 71.9301 302.608 73.5 303C75.1 303.4 104.5 310.833 119 314.5L135 324.5C133.667 325 130.9 325.9 130.5 325.5C130.1 325.1 126 326 124 326.5L109.5 328L93 326.5L83 324C80 323 73.9 321 73.5 321C73.1 321 64 315.333 59.5 312.5L52.5 306.5L38.6422 298.086C38.4242 298.057 38.21 298.028 38 298H38.5Z"
      fill="#A59E9E"
    />
    <path
      d="M38.5 298L52.5 306.5L59.5 312.5C64 315.333 73.1 321 73.5 321C73.9 321 80 323 83 324L93 326.5L109.5 328L124 326.5C126 326 130.1 325.1 130.5 325.5C130.9 325.9 133.667 325 135 324.5L119 314.5C104.5 310.833 75.1 303.4 73.5 303C71.9 302.6 49.1667 299.5 38 298H38.5Z"
      stroke="black"
    />
    <path
      d="M89 319C89.8 319.4 106 322.5 114 324C110.333 324.5 102.4 325.4 100 325C97.6 324.6 92.3333 323.167 90 322.5L80 319L72 313C77.3333 314.833 88.2 318.6 89 319Z"
      fill="white"
      stroke="black"
    />
    <path
      d="M38.4999 234C70.9999 240.5 137.6 255.6 144 264C150.4 272.4 140.333 307.5 134.5 324C99.6666 315.333 29.7999 297.3 28.9999 294.5C28.9577 294.352 28.9366 294.104 28.9351 293.764L2.5 204L15.5 150.5C23.1666 176.667 38.4999 230 38.4999 234Z"
      fill="#9D9999"
    />
    <path
      d="M38.4999 234C70.9999 240.5 137.6 255.6 144 264C150.4 272.4 140.333 307.5 134.5 324C99.6666 315.333 29.7999 297.3 28.9999 294.5C28.9577 294.352 28.9366 294.104 28.9351 293.764M38.4999 234C38.4999 230 23.1666 176.667 15.5 150.5L2.5 204L28.9351 293.764M38.4999 234C35.1847 251.997 28.9089 287.668 28.9351 293.764"
      stroke="black"
      strokeWidth="3"
    />
    <path
      d="M186 172.5C183.667 186.167 174.5 213.915 150.5 216.315C120.5 219.315 81.5 225.815 67 191.315C55.4 163.715 59 153.333 63 151.5M38.4999 129.5C30.8333 138.167 17.2999 159.7 24.4999 176.5C33.4999 197.5 31.5 200 44.5 214C57.5 228 89.5 247 107 247C124.5 247 154 251.5 178.5 240.5C203 229.5 219 196 219 188C219 181.6 216 166.667 214.5 160L38.4999 129.5Z"
      stroke="black"
      strokeWidth="3"
    />
    <g filter="url(#filter7_i_678_152)">
      <path
        d="M120.5 151C119.3 151 79.6667 155 60 157L63 178L68.5 194.5L80 208.5L94.5 217.5L118 219.5L154.5 216.5L168 211L176 201L186 178L188 171.5L184.5 159.5C163.667 156.667 121.7 151 120.5 151Z"
        fill="#A59E9E"
      />
    </g>
    <path
      d="M120.5 151C119.3 151 79.6667 155 60 157L63 178L68.5 194.5L80 208.5L94.5 217.5L118 219.5L154.5 216.5L168 211L176 201L186 178L188 171.5L184.5 159.5C163.667 156.667 121.7 151 120.5 151Z"
      stroke="black"
    />
    <path
      d="M158 207L171.5 195L166.5 198.5L154.5 204L143 208L128.5 209L112 207L97.5 202.5L83.5 195.5L72 186C74.5 188.833 79.7 194.7 80.5 195.5C81.3 196.3 85.1667 199.833 87 201.5L95.5 207L110.5 212L127 214L142.5 212.5L158 207Z"
      fill="white"
      stroke="black"
    />
    <path
      d="M156.5 197.5L165.5 192.5L172.5 185.033L160 191.5L146.5 196L131.5 198L121 197.5L108.5 195L98.5 191.5L87 185.5L75 175.5L83.5 185.5L91 191.5L103.5 198L120 203H134.5L146.5 201.5L156.5 197.5Z"
      fill="white"
    />
    <path
      d="M173 184.5L172.5 185.033M172.5 185.033L165.5 192.5L156.5 197.5L146.5 201.5L134.5 203H120L103.5 198L91 191.5L83.5 185.5L75 175.5L87 185.5L98.5 191.5L108.5 195L121 197.5L131.5 198L146.5 196L160 191.5L172.5 185.033Z"
      stroke="black"
    />
    <path
      d="M82.5 168L78 166L79.5 168L84 172.5L89.5 178L95.5 181.5L104.5 186L113 189L122 191L129 192L137.5 191.5L146 190.5L154 188L161 184.5L168 180H162L157 182L144 185.5L133.5 186.5H126L112 184.5L104 181.5L95.5 178L89.5 174L82.5 168Z"
      fill="white"
      stroke="black"
    />
    <g filter="url(#filter8_i_678_152)">
      <ellipse
        cx="135.127"
        cy="92.9119"
        rx="109.096"
        ry="89.58"
        transform="rotate(1.56136 135.127 92.9119)"
        fill="#D9D9D9"
      />
    </g>
    <path
      d="M242.683 95.8436C241.364 144.208 192.418 182.586 132.727 180.959C73.0353 179.332 26.2525 138.345 27.5708 89.9802C28.8891 41.6159 77.8352 3.23757 137.527 4.86461C197.218 6.49166 244.001 47.4793 242.683 95.8436Z"
      stroke="black"
      strokeWidth="3"
    />
    <path
      d="M194.653 60.2742C193.719 70.9735 187.05 80.2698 176.791 86.6103C166.531 92.9509 152.763 96.2699 137.922 94.9753C123.082 93.6807 110.096 88.0276 101.09 80.0064C92.0832 71.9853 87.1243 61.6745 88.0576 50.9752C88.991 40.276 95.6604 30.9796 105.92 24.6392C116.179 18.2986 129.948 14.9796 144.788 16.2742C159.628 17.5688 172.614 23.2218 181.621 31.2431C190.627 39.2642 195.586 49.575 194.653 60.2742Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="3"
    />
    <path
      d="M101 43L112.5 75L152.5 87L183 65.5L172.5 35L131 23L101 43Z"
      stroke="black"
      strokeWidth="3"
    />
    <path
      d="M129.5 47.5L108 59L101 42.5L131.5 22.5L129.5 47.5Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="3"
    />
    <g filter="url(#filter9_i_678_152)">
      <path d="M167 59L130 48L131 23L171.5 35.5L167 59Z" fill="#D9D9D9" />
    </g>
    <path
      d="M167 59L130 48L131 23L171.5 35.5L167 59Z"
      stroke="black"
      strokeWidth="3"
    />
    <g filter="url(#filter10_i_678_152)">
      <path
        d="M184 65L171.5 72.5L151.5 87L111.5 74.5L107 58H108L130 46.5L167 59L172.5 35.5L184 65Z"
        fill="#D9D9D9"
      />
    </g>
    <path
      d="M167 59L171.5 72.5M167 59L172.5 35.5L184 65L171.5 72.5M167 59L130 46.5L108 58H107L111.5 74.5L151.5 87L171.5 72.5"
      stroke="black"
      strokeWidth="3"
    />
    <g filter="url(#filter11_d_678_152)">
      <path
        d="M355 151.5C355 151 361 140.5 361.5 140.5C361.9 140.5 366 136.833 368 135L374 131L382 128L392.5 127H396H399L387 129L375 135.5L368 143L361.5 155L359 167V183L363 199.5L366 207L373.5 220C374.833 221.667 377.6 225.1 378 225.5C378.4 225.9 385.5 231.333 389 234L398.5 238.5L406.5 241L417.5 240.5C419.667 240.167 424 239.4 424 239C424 238.6 429 235.833 431.5 234.5L439 228L441 225L446.5 214.5L448.5 207L449.836 202.101L450 198.25V197L449 187.5V181C449 180.6 448.333 177.167 448 175.5L450.5 186.5L450 198.25V201.5L449.836 202.101L449.5 210L444.5 222.5L442 227L437.5 233L434 235L429.5 238.5L423 241.5L419 243L414 244H406L397.5 242.5L391 240.5L384.5 236.5L381.5 234.5L375.5 230L370 224.5L365 218L362.5 215.5L359.5 210.5L355.5 202.5L353 196L349.5 182V171L350.5 164.5L353.5 153L355 151.5Z"
        fill="white"
      />
      <path
        d="M355 151.5C355 151 361 140.5 361.5 140.5C361.9 140.5 366 136.833 368 135L374 131L382 128L392.5 127H396H399L387 129L375 135.5L368 143L361.5 155L359 167V183L363 199.5L366 207L373.5 220C374.833 221.667 377.6 225.1 378 225.5C378.4 225.9 385.5 231.333 389 234L398.5 238.5L406.5 241L417.5 240.5C419.667 240.167 424 239.4 424 239C424 238.6 429 235.833 431.5 234.5L439 228L441 225L446.5 214.5L448.5 207L450 201.5V197L449 187.5C449 185.5 449 181.4 449 181C449 180.6 448.333 177.167 448 175.5L450.5 186.5L449.5 210L444.5 222.5L442 227L437.5 233L434 235L429.5 238.5L423 241.5L419 243L414 244H406L397.5 242.5L391 240.5L384.5 236.5L381.5 234.5L375.5 230L370 224.5L365 218L362.5 215.5L359.5 210.5L355.5 202.5L353 196L349.5 182V171L350.5 164.5L353.5 153L355 151.5Z"
        stroke="black"
      />
      <g filter="url(#filter12_i_678_152)">
        <path
          d="M499 196L460 285L365 281L308 174.5L350.5 78.5L443.5 90.5L499 196Z"
          fill="#D9D9D9"
        />
      </g>
      <path
        d="M499 196L460 285L365 281L308 174.5L350.5 78.5L443.5 90.5L499 196Z"
        stroke="black"
        strokeWidth="3"
      />
      <g filter="url(#filter13_i_678_152)">
        <ellipse
          cx="402.927"
          cy="183.5"
          rx="77.3629"
          ry="101.449"
          transform="rotate(-11.1495 402.927 183.5)"
          fill="#D9D9D9"
        />
      </g>
      <path
        d="M477.358 168.831C482.718 196.023 479.152 222.243 469.124 242.642C459.096 263.039 442.658 277.542 422.254 281.563C401.851 285.584 381.139 278.404 364.122 263.337C347.103 248.269 333.855 225.362 328.496 198.17C323.137 170.978 326.702 144.758 336.73 124.359C346.758 103.961 363.197 89.4591 383.6 85.4378C404.003 81.4165 424.716 88.5968 441.733 103.664C458.751 118.732 471.999 141.639 477.358 168.831Z"
        stroke="black"
        strokeWidth="3"
      />
      <g filter="url(#filter14_i_678_152)">
        <ellipse
          cx="401"
          cy="184.86"
          rx="51.5249"
          ry="66.5364"
          transform="rotate(-17.3236 401 184.86)"
          fill="#D9D9D9"
          fillOpacity="0.1"
        />
      </g>
      <path
        d="M448.756 169.965C459.564 204.616 446.421 238.819 420.366 246.947C394.31 255.074 364.053 234.408 353.244 199.756C342.436 165.105 355.579 130.901 381.634 122.774C407.69 114.647 437.947 135.313 448.756 169.965Z"
        stroke="black"
        strokeWidth="3"
      />
      <path
        d="M444.5 178.5C445 183.667 446 194.2 446 195L435.5 183.5L428.5 168L425 146.5L433 155L438.5 164L444.5 178.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M435.5 196.5L444.5 206L439.5 203L432.5 196L426 187.5L422.5 183L417 168L415 150L416.5 141L420.5 143.5V158.5L422.5 170.5L428 185.5L435.5 196.5Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M418.5 189.5L426.5 201L436.834 211.826L428 206.5L420.5 198.5L412.5 187L408 175.5L405.5 164.5L405 158.5L406 150.5L407 146L410 137.5L412 139L410 149.5L409.5 158C410 160.833 411 166.6 411 167C411 167.4 413 175.167 414 179L418.5 189.5Z"
        fill="white"
      />
      <path
        d="M437 212L436.834 211.826M436.834 211.826L426.5 201L418.5 189.5L414 179C413 175.167 411 167.4 411 167C411 166.6 410 160.833 409.5 158L410 149.5L412 139L410 137.5L407 146L406 150.5L405 158.5L405.5 164.5L408 175.5L412.5 187L420.5 198.5L428 206.5L436.834 211.826Z"
        stroke="black"
      />
      <path
        d="M402.5 139L403.5 136L400.5 139.5L396 149L395 155V167.5L397 177L400.5 186.5L405.5 196L413.5 206L421 212L428 216.5L420.5 209.5L412 199.5L403.5 183.5L401.5 178L399.5 159L400.5 147.5L402.5 139Z"
        fill="white"
        stroke="black"
      />
      <path
        d="M391.5 146.5L394.5 138L389 145L385.5 154.5L384.5 163.5C384.667 166.833 385 173.6 385 174C385 174.4 386.333 179.167 387 181.5L389 188.5L392 194.5L396.5 202L402.5 209L407.5 214L413 217.5L416.5 220L415.5 218.5L413.5 216.5L411 215L406.5 209.5L403.5 206L398 197L390.5 175L389.5 159L391.5 146.5Z"
        fill="white"
      />
      <path
        d="M380.5 150.5L384 142.5H383.5L381 145.5C379.667 148 377 153.1 377 153.5C377 153.9 375.667 159.667 375 162.5L374.5 170L375.5 179.5L379 191.5C380.833 195.667 384.6 204.1 385 204.5C385.4 204.9 389.833 210.333 392 213L399.5 220L407 224L399 217L392.5 209.5L387 200L382 187.5L379 166.5L380.5 150.5Z"
        fill="white"
      />
      <path
        d="M391.5 146.5L394.5 138L389 145L385.5 154.5L384.5 163.5C384.667 166.833 385 173.6 385 174C385 174.4 386.333 179.167 387 181.5L389 188.5L392 194.5L396.5 202L402.5 209L407.5 214L413 217.5L416.5 220L415.5 218.5L413.5 216.5L411 215L406.5 209.5L403.5 206L398 197L390.5 175L389.5 159L391.5 146.5Z"
        stroke="black"
      />
      <path
        d="M380.5 150.5L384 142.5H383.5L381 145.5C379.667 148 377 153.1 377 153.5C377 153.9 375.667 159.667 375 162.5L374.5 170L375.5 179.5L379 191.5C380.833 195.667 384.6 204.1 385 204.5C385.4 204.9 389.833 210.333 392 213L399.5 220L407 224L399 217L392.5 209.5L387 200L382 187.5L379 166.5L380.5 150.5Z"
        stroke="black"
      />
      <path
        d="M374 146L370.5 155L369 164.5L369.5 177.5L370.5 186C371.167 188.333 372.5 193.1 372.5 193.5C372.5 193.9 375.5 201.667 377 205.5L384.5 216.5L390.5 222L396 227.5C396.4 227.9 389.5 223.667 386 221.5L377.5 212L372.5 204L367 190.5L365.5 184C365.184 179.895 364.569 171.99 364.505 171.509C364.502 171.524 364.5 171.521 364.5 171.5C364.5 171.479 364.502 171.483 364.505 171.509C364.569 171.252 365.184 165.5 365.5 162.5C366.5 160 368.5 154.9 368.5 154.5C368.5 154.1 372.167 148.667 374 146Z"
        fill="white"
      />
      <path
        d="M374 146L370.5 155L369 164.5L369.5 177.5L370.5 186C371.167 188.333 372.5 193.1 372.5 193.5C372.5 193.9 375.5 201.667 377 205.5L384.5 216.5L390.5 222C392.167 223.667 395.6 227.1 396 227.5C396.4 227.9 389.5 223.667 386 221.5L377.5 212L372.5 204L367 190.5L365.5 184C365.167 179.667 364.5 171.1 364.5 171.5C364.5 171.9 365.167 165.667 365.5 162.5C366.5 160 368.5 154.9 368.5 154.5C368.5 154.1 372.167 148.667 374 146Z"
        stroke="black"
      />
      <path
        d="M522 263.5L460 284.5L501 196L563 173L522 263.5Z"
        fill="#D9D9D9"
        stroke="black"
        strokeWidth="3"
      />
      <path
        d="M506.5 67.5003C523.3 76.7003 551.833 141 564 172L502.5 196L444.5 89.0003C458.167 78.0003 489.7 58.3003 506.5 67.5003Z"
        fill="#D9D9D9"
        stroke="black"
        strokeWidth="3"
      />
      <path
        d="M348.5 79.5L446.5 90C442.1 83.6 480.333 71 500 65.5L411 55L348.5 79.5Z"
        fill="#D9D9D9"
        stroke="black"
        strokeWidth="3"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_678_152"
        x="104"
        y="340.467"
        width="464.112"
        height="299.033"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="3" dy="25" />
        <feGaussianBlur stdDeviation="12.5" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_678_152"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_678_152"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_di_678_152"
        x="263.598"
        y="340.467"
        width="280.513"
        height="193.379"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_678_152"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_678_152"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="20" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect2_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter2_i_678_152"
        x="126"
        y="358.989"
        width="190.175"
        height="234.511"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="40" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter3_d_678_152"
        x="173.953"
        y="455.916"
        width="30.5254"
        height="72.2227"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_678_152"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_678_152"
          result="shape"
        />
      </filter>
      <filter
        id="filter4_i_678_152"
        x="138.867"
        y="453.351"
        width="48.1914"
        height="59.667"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="20" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter5_i_678_152"
        x="142.953"
        y="497.331"
        width="62.7927"
        height="44.1631"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="5" dy="12" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter6_i_678_152"
        x="12.8201"
        y="121.877"
        width="221.362"
        height="148.254"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="30" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter7_i_678_152"
        x="59.4314"
        y="150.5"
        width="133.091"
        height="73.502"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="30" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter8_i_678_152"
        x="26.0435"
        y="3.31445"
        width="222.166"
        height="183.194"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="30" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter9_i_678_152"
        x="128.455"
        y="16.9912"
        width="44.7712"
        height="43.9189"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="-12" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter10_i_678_152"
        x="101.036"
        y="30.4502"
        width="84.8213"
        height="62.2061"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="-21" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter11_d_678_152"
        x="302.332"
        y="53.4658"
        width="267.613"
        height="241.577"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_678_152"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_678_152"
          result="shape"
        />
      </filter>
      <filter
        id="filter12_i_678_152"
        x="306.332"
        y="76.8691"
        width="198.332"
        height="213.673"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="31" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter13_i_678_152"
        x="324.511"
        y="82.835"
        width="160.832"
        height="205.331"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="15" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
      <filter
        id="filter14_i_678_152"
        x="347.958"
        y="119.5"
        width="110.084"
        height="134.721"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="30" dy="15" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_678_152"
        />
      </filter>
    </defs>
  </svg>
);
