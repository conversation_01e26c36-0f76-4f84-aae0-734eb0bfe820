export const PortaVaiVemDupla = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="649"
    height="628"
    viewBox="0 0 649 628"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="326"
      y="32"
      width="288"
      height="595"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="289"
      y="324.323"
      width="12"
      height="13.7079"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M290 326.016C290 325.739 290.224 325.516 290.5 325.516H299.5C299.776 325.516 300 325.739 300 326.016V336.935C300 337.212 299.776 337.435 299.5 337.435H290.5C290.224 337.435 290 337.212 290 336.935V326.016Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter0_i_767_150)">
      <path
        d="M290 306.914C290 304.705 291.791 302.914 294 302.914H296C298.209 302.914 300 304.705 300 306.914V307.945C300 310.154 298.209 311.945 296 311.945H294C291.791 311.945 290 310.154 290 307.945V306.914Z"
        fill="#474747"
      />
    </g>
    <rect
      x="289"
      y="298.9"
      width="12"
      height="18.0609"
      fill="#010101"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter1_d_767_150)">
      <path
        d="M258 303.917H295.986C298.203 303.917 300 305.714 300 307.931V307.931C300 310.147 298.203 311.944 295.986 311.944H258V303.917Z"
        fill="#222121"
      />
      <path
        d="M258.5 304.417H295.986C297.927 304.417 299.5 305.99 299.5 307.931C299.5 309.871 297.927 311.444 295.986 311.444H258.5V304.417Z"
        stroke="white"
        strokeOpacity="0.4"
      />
    </g>
    <rect
      x="35"
      y="28"
      width="288"
      height="599"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect x="36" y="570" width="5" height="24" fill="black" />
    <rect x="37" y="568" width="3" height="2" fill="black" />
    <rect x="37" y="594" width="3" height="2" fill="black" />
    <rect x="36" y="84" width="5" height="24" fill="black" />
    <rect x="37" y="82" width="3" height="2" fill="black" />
    <rect x="37" y="108" width="3" height="2" fill="black" />
    <rect x="36" y="148" width="5" height="24" fill="black" />
    <rect x="37" y="146" width="3" height="2" fill="black" />
    <rect x="37" y="172" width="3" height="2" fill="black" />
    <rect x="609" y="572" width="5" height="24" fill="black" />
    <rect x="610" y="570" width="3" height="2" fill="black" />
    <rect x="610" y="596" width="3" height="2" fill="black" />
    <rect x="609" y="86" width="5" height="24" fill="black" />
    <rect x="610" y="84" width="3" height="2" fill="black" />
    <rect x="610" y="110" width="3" height="2" fill="black" />
    <rect x="609" y="150" width="5" height="24" fill="black" />
    <rect x="610" y="148" width="3" height="2" fill="black" />
    <rect x="610" y="174" width="3" height="2" fill="black" />
    <rect x="614" y="32" width="35" height="595.168" fill="black" />
    <rect y="30" width="35" height="597" fill="black" />
    <rect x="323" y="34.7256" width="3.04348" height="592.274" fill="black" />
    <g filter="url(#filter2_i_767_150)">
      <rect
        x="0.5"
        y="0.5"
        width="648"
        height="33.7262"
        fill="black"
        stroke="black"
      />
    </g>
    <defs>
      <filter
        id="filter0_i_767_150"
        x="290"
        y="302.914"
        width="10"
        height="13.0303"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_767_150"
        />
      </filter>
      <filter
        id="filter1_d_767_150"
        x="254"
        y="303.917"
        width="50"
        height="16.0273"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_767_150"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_767_150"
          result="shape"
        />
      </filter>
      <filter
        id="filter2_i_767_150"
        x="0"
        y="0"
        width="649"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_767_150"
        />
      </filter>
    </defs>
  </svg>
);
