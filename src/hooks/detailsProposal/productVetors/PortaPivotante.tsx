export const PortaPivotante = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="424"
    height="630"
    viewBox="0 0 424 630"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="35" y="35" width="354" height="592" fill="#D9D9D9" />
    <rect x="389" y="34" width="35" height="595.168" fill="black" />
    <rect y="30" width="35" height="599" fill="black" />
    <g filter="url(#filter0_i_763_240)">
      <rect
        x="0.5"
        y="0.5"
        width="423"
        height="33.7262"
        fill="black"
        stroke="black"
      />
    </g>
    <g filter="url(#filter1_d_763_240)">
      <rect x="333" y="188" width="11" height="283" fill="black" />
    </g>
    <circle cx="371.5" cy="352.5" r="7.5" fill="black" />
    <defs>
      <filter
        id="filter0_i_763_240"
        x="0"
        y="0"
        width="424"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_763_240"
        />
      </filter>
      <filter
        id="filter1_d_763_240"
        x="333"
        y="188"
        width="23"
        height="291"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="8" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_763_240"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_763_240"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
