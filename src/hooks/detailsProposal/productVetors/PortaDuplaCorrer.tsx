export const PortaDuplaCorrer = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="890"
    height="629"
    viewBox="0 0 890 629"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_i_635_398)">
      <rect width="466" height="34.7262" fill="black" />
      <path
        d="M0 31.832H434V34.7259H0V31.832Z"
        fill="white"
        fillOpacity="0.25"
      />
    </g>
    <rect
      x="136"
      y="34.7266"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="390"
      y="279.739"
      width="12"
      height="34.4457"
      fill="#010101"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="390"
      y="319.926"
      width="12"
      height="22.0069"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M391 322.34C391 322.064 391.224 321.84 391.5 321.84H400.5C400.776 321.84 401 322.064 401 322.34V340.476C401 340.752 400.776 340.976 400.5 340.976H391.5C391.224 340.976 391 340.752 391 340.476V322.34Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter1_i_635_398)">
      <rect width="466" height="34.7262" fill="black" />
      <path
        d="M0 31.832H434V34.7259H0V31.832Z"
        fill="white"
        fillOpacity="0.25"
      />
    </g>
    <g filter="url(#filter2_i_635_398)">
      <rect x="424" width="466" height="34.7262" fill="black" />
      <path
        d="M424 31.832H858V34.7259H424V31.832Z"
        fill="white"
        fillOpacity="0.25"
      />
    </g>
    <rect
      x="136"
      y="34.7266"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="424"
      y="35"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="385"
      y="322"
      width="12"
      height="22.0069"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M386 324.414C386 324.138 386.224 323.914 386.5 323.914H395.5C395.776 323.914 396 324.138 396 324.414V342.551C396 342.827 395.776 343.051 395.5 343.051H386.5C386.224 343.051 386 342.827 386 342.551V324.414Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter3_d_635_398)">
      <path d="M385 243H397V422H385V243Z" fill="#010101" />
      <path d="M385 243H397V422H385V243Z" stroke="black" strokeWidth="2" />
    </g>
    <g filter="url(#filter4_d_635_398)">
      <path d="M454 243H466V422H454V243Z" fill="#010101" />
      <path d="M454 243H466V422H454V243Z" stroke="black" strokeWidth="2" />
    </g>
    <defs>
      <filter
        id="filter0_i_635_398"
        x="0"
        y="0"
        width="466"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_635_398"
        />
      </filter>
      <filter
        id="filter1_i_635_398"
        x="0"
        y="0"
        width="466"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_635_398"
        />
      </filter>
      <filter
        id="filter2_i_635_398"
        x="424"
        y="0"
        width="466"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_635_398"
        />
      </filter>
      <filter
        id="filter3_d_635_398"
        x="384"
        y="242"
        width="26"
        height="189"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="8" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_635_398"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_635_398"
          result="shape"
        />
      </filter>
      <filter
        id="filter4_d_635_398"
        x="453"
        y="242"
        width="26"
        height="189"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="8" dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_635_398"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_635_398"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
