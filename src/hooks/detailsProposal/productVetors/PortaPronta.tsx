export const PortaPronta = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="358"
    height="627"
    viewBox="0 0 358 627"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="35"
      y="33"
      width="288"
      height="592.274"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="289"
      y="324.338"
      width="12"
      height="13.6616"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M290 326.026C290 325.75 290.224 325.526 290.5 325.526H299.5C299.776 325.526 300 325.75 300 326.026V336.906C300 337.182 299.776 337.406 299.5 337.406H290.5C290.224 337.406 290 337.182 290 336.906V326.026Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter0_i_635_313)">
      <path
        d="M290 307C290 304.791 291.791 303 294 303H296C298.209 303 300 304.791 300 307V308C300 310.209 298.209 312 296 312H294C291.791 312 290 310.209 290 308V307Z"
        fill="#474747"
      />
    </g>
    <rect
      x="289"
      y="299"
      width="12"
      height="18"
      fill="#010101"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter1_d_635_313)">
      <path
        d="M258 304H296C298.209 304 300 305.791 300 308C300 310.209 298.209 312 296 312H258V304Z"
        fill="#222121"
      />
      <path
        d="M258.5 304.5H296C297.933 304.5 299.5 306.067 299.5 308C299.5 309.933 297.933 311.5 296 311.5H258.5V304.5Z"
        stroke="white"
        strokeOpacity="0.4"
      />
    </g>
    <rect x="323" y="31.832" width="35" height="595.168" fill="black" />
    <rect y="30" width="35" height="595.168" fill="black" />
    <rect x="323" y="34.7256" width="3.04348" height="592.274" fill="black" />
    <g filter="url(#filter2_i_635_313)">
      <rect
        x="0.5"
        y="0.5"
        width="357"
        height="33.7262"
        fill="black"
        stroke="black"
      />
    </g>
    <defs>
      <filter
        id="filter0_i_635_313"
        x="290"
        y="303"
        width="10"
        height="13"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_635_313"
        />
      </filter>
      <filter
        id="filter1_d_635_313"
        x="254"
        y="304"
        width="50"
        height="16"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_635_313"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_635_313"
          result="shape"
        />
      </filter>
      <filter
        id="filter2_i_635_313"
        x="0"
        y="0"
        width="358"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_635_313"
        />
      </filter>
    </defs>
  </svg>
);
