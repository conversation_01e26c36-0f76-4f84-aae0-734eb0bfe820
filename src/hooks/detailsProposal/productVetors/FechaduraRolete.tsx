export const FechaduraRolete = () => (
  <svg
    width="284"
    height="210"
    viewBox="0 0 284 210"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="284" height="210" fill="white" />
    <g filter="url(#filter0_i_870_162)">
      <rect x="57" y="19" width="170" height="170" rx="85" fill="#D9D9D9" />
    </g>
    <rect
      x="58.5"
      y="20.5"
      width="167"
      height="167"
      rx="83.5"
      stroke="black"
      strokeWidth="3"
    />
    <rect
      x="117"
      y="57"
      width="49"
      height="93"
      rx="24.5"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="3"
    />
    <circle
      cx="141.5"
      cy="80.5"
      r="16"
      fill="#313030"
      stroke="black"
      strokeWidth="3"
    />
    <defs>
      <filter
        id="filter0_i_870_162"
        x="57"
        y="19"
        width="170"
        height="174"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_870_162"
        />
      </filter>
    </defs>
  </svg>
);
