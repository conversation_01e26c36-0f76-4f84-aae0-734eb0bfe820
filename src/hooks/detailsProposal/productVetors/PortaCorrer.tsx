export const PortaCorrer = () => (
  <svg
    className="w-full p-1 h-full flex justify-center items-center"
    width="466"
    height="628"
    viewBox="0 0 466 628"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="431" y="31.8325" width="35" height="595.168" fill="black" />
    <rect x="431" y="34.7261" width="3.04348" height="592.274" fill="black" />
    <g filter="url(#filter0_i_579_183)">
      <rect width="466" height="34.7262" fill="black" />
      <path
        d="M0 31.8325H434V34.7264H0V31.8325Z"
        fill="white"
        fillOpacity="0.25"
      />
    </g>
    <path
      d="M136 34.7261H424V627H136V34.7261Z"
      fill="#D9D9D9"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="390"
      y="279.738"
      width="12"
      height="34.4457"
      fill="#010101"
      stroke="black"
      strokeWidth="2"
    />
    <rect
      x="390"
      y="319.925"
      width="12"
      height="22.0069"
      rx="0.5"
      fill="black"
      stroke="black"
      strokeWidth="2"
    />
    <path
      d="M391 322.339C391 322.063 391.224 321.839 391.5 321.839H400.5C400.776 321.839 401 322.063 401 322.339V340.475C401 340.751 400.776 340.975 400.5 340.975H391.5C391.224 340.975 391 340.751 391 340.475V322.339Z"
      fill="#202020"
      stroke="black"
      strokeWidth="2"
    />
    <g filter="url(#filter1_i_579_183)">
      <rect width="466" height="34.7262" fill="black" />
      <path
        d="M0 31.8325H434V34.7264H0V31.8325Z"
        fill="white"
        fillOpacity="0.25"
      />
    </g>
    <defs>
      <filter
        id="filter0_i_579_183"
        x="0"
        y="0"
        width="466"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_579_183"
        />
      </filter>
      <filter
        id="filter1_i_579_183"
        x="0"
        y="0"
        width="466"
        height="38.7266"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="BackgroundImageFix"
          result="shape"
        />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2" />
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"
        />
        <feBlend
          mode="normal"
          in2="shape"
          result="effect1_innerShadow_579_183"
        />
      </filter>
    </defs>
  </svg>
);
