import { IEventsMapsProps, IMapsEventHandlers } from "@/types/map/events";
import { ICoordinates } from "@/types/proposal";
import { MapEvent } from "@vis.gl/react-google-maps";
import { useCallback, useEffect, useState } from "react";

const adjustMapZoomToBounds = ({
  map,
  locations,
  minZoom = 4,
}: {
  map: google.maps.Map;
  locations: ICoordinates[];
  minZoom?: number;
}) => {
  if (!map || !Array.isArray(locations) || locations.length === 0) {
    console.error("Localizações não fornecidas.");
    return;
  }

  const locationsWithPositions = locations.map((location) => ({
    ...location,
    position: {
      lat: location.latitude,
      lng: location.longitude,
    },
  }));

  const bounds = new google.maps.LatLngBounds();
  locationsWithPositions.forEach((location) =>
    bounds.extend(location.position),
  );
  map.fitBounds(bounds);
  map.setOptions({ minZoom: minZoom });
};

const setMapFocus = ({
  mapRef,
  focusCoordinates,
}: {
  mapRef: React.RefObject<google.maps.Map>;
  focusCoordinates: google.maps.LatLngLiteral;
}) => {
  if (mapRef.current) {
    mapRef.current.setZoom(12);
    mapRef.current.panTo(focusCoordinates);
  }
};

export const useMapEventHandlers = ({
  mapRef,
  locations,
  theme,
}: IEventsMapsProps): IMapsEventHandlers => {
  const [isTrackingStarted, setTrackingStarted] = useState<boolean>(false);

  const handleAdjustMapZoomToBounds = useCallback(
    (map: google.maps.Map) => adjustMapZoomToBounds({ map, locations }),
    [locations],
  );

  const handleSetMapFocus = useCallback(
    (focusCoordinates: google.maps.LatLngLiteral) =>
      setMapFocus({ mapRef, focusCoordinates }),
    [mapRef],
  );

  const getPositionWithName = useCallback(
    ({ name }: { name: string }): google.maps.LatLngLiteral | null => {
      const position = locations.find((location) =>
        location.nome.toLowerCase().includes(name.toLowerCase()),
      );
      return position
        ? {
            lat: position.latitude,
            lng: position.longitude,
          }
        : null;
    },
    [locations],
  );

  const onTilesLoaded = useCallback(
    (eventMap: MapEvent<unknown>) => {
      const { map: googleMapInstance } = eventMap;
      if (!isTrackingStarted && locations.length >= 2) {
        setTrackingStarted(true);
        handleAdjustMapZoomToBounds(googleMapInstance);
      } else if (!isTrackingStarted && locations.length === 1) {
        setTrackingStarted(true);
        handleSetMapFocus(getPositionWithName({ name: "Pormade Portas" })!);
      }
      mapRef.current = googleMapInstance;
    },
    [
      handleAdjustMapZoomToBounds,
      isTrackingStarted,
      getPositionWithName,
      locations,
      mapRef,
      handleSetMapFocus,
    ],
  );

  useEffect(() => {
    const executeAfterDelay = ({ action }: { action: () => void }) => {
      setTimeout(() => {
        action();
      }, 800);
    };

    if (mapRef.current && locations.length >= 2) {
      executeAfterDelay({
        action: () => handleAdjustMapZoomToBounds(mapRef.current!),
      });
    } else if (mapRef.current && locations.length === 1) {
      executeAfterDelay({
        action: () =>
          handleSetMapFocus(getPositionWithName({ name: "Pormade Portas" })!),
      });
    }
  }, [
    handleAdjustMapZoomToBounds,
    theme,
    mapRef,
    locations,
    getPositionWithName,
    handleSetMapFocus,
  ]);

  return {
    adjustMapZoomToBounds: handleAdjustMapZoomToBounds,
    setMapFocus: handleSetMapFocus,
    onTilesLoaded,
    getPositionWithName,
  };
};
