import { useAcessContext } from "@/contexts/acess/acessContext";
import { DeliveryInterface } from "@/types/deliveries";
import { useCallback } from "react";

export const useActiveDelivery = () => {
  const { currentProductionSelected } = useAcessContext();

  const getCurrentDelivery = useCallback(
    ({
      delivery,
      isNew,
    }: {
      delivery: DeliveryInterface[] | undefined | null;
      isNew: boolean | undefined;
    }) => {
      if (isNew) {
        return delivery?.find(
          (item) => item.order === currentProductionSelected,
        );
      }
      return delivery?.[0];
    },
    [currentProductionSelected],
  );

  return { getCurrentDelivery };
};
