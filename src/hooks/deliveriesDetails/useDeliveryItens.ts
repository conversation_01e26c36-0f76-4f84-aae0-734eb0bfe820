import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import { useEffect, useState } from "react";
import useDeliveriesDetails from "./useDeliveriesDetails";

export const useDeliveryItens = () => {
  const { events } = useDeliveriesDetails();
  const regexDateAndTime = /\d{2}\/\d{2}\/\d{4}, \d{2}:\d{2}/;
  const regexDate = /\d{2}\/\d{2}\/\d{4}/;
  const [visibleIndices, setVisibleIndices] = useState<number[]>([]);
  const { animationKeyDelivery } = useDetailsProductContext();
  const [lastAnimationKey, setLastAnimationKey] = useState<number>(10);

  useEffect(() => {
    if (lastAnimationKey !== animationKeyDelivery) {
      setVisibleIndices([]);
      for (const event of events) {
        setTimeout(
          () => {
            setVisibleIndices((prev) => [...prev, events.indexOf(event)]);
          },
          events.indexOf(event) * 200,
        );
      }
      setLastAnimationKey(animationKeyDelivery);
    }
  }, [events, animationKeyDelivery, lastAnimationKey]);

  return {
    regexDate,
    regexDateAndTime,
    visibleIndices,
    events,
    animationKeyDelivery,
  };
};
