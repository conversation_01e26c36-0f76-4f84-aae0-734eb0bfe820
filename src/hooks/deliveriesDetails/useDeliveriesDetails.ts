"use client";
import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import { DeliveryType, EventType } from "@/types/deliveries";
import { useCallback, useEffect, useState } from "react";
import { useCurrentProduction } from "../productionProgress/useCurrentProduction";
import { useActiveDelivery } from "./useCurrentDeliveries";

const useDeliveriesDetails = () => {
  const { deliveriesData } = useDataContext();
  const [events, setEvents] = useState<EventType[]>([]);
  const { scrollEventsRef } = useDetailsProductContext();
  const { productionData, proposalData } = useDataContext();
  const { currentProductionSelected } = useAcessContext();
  const { getCurrentDelivery } = useActiveDelivery();
  const { getCurrentProduction } = useCurrentProduction();

  const getEventsFromDeliveriesData = useCallback(
    (deliveriesData: DeliveryType) => {
      const invalidEventTypes = [
        "checklist concluido",
        "repetidos",
        "veículo passou por aqui",
        "lista de tarefas concluída",
      ];
      if (!deliveriesData) return [];
      return deliveriesData.events.filter(
        (event: EventType) =>
          !invalidEventTypes.includes(event.description.toLowerCase()),
      );
    },
    [],
  );

  const replaceDuplicates = useCallback(
    (events: EventType[]) => {
      const uniqueEvents: EventType[] = [];
      let coletaCount = 0;

      let fotoEntregaReplaced = false;

      if (
        getCurrentDelivery({
          delivery: deliveriesData,
          isNew: proposalData?.newProposal,
        })?.deliveryByPartner?.isDoubleDeliveryFretefy
      ) {
        events.forEach((event: EventType) => {
          if (event.description.toLowerCase() === "registro de coleta") {
            coletaCount++;

            if (coletaCount === 2) {
              event.description = "Coletado por Transportador Parceiro";
            }
          }

          if (event.description.toLowerCase() === "foto entrega") {
            if (!fotoEntregaReplaced) {
              event.description = "Entregue ao transportador terceiro";
              fotoEntregaReplaced = true;
            } else {
              return;
            }
          }

          if (
            !uniqueEvents.some(
              (uniqueEvent: EventType) =>
                uniqueEvent.description.toLowerCase() ===
                event.description.toLowerCase(),
            )
          ) {
            uniqueEvents.push(event);
          }
        });
      } else {
        events.forEach((event: EventType) => {
          if (
            !uniqueEvents.some(
              (uniqueEvent: EventType) =>
                uniqueEvent.description.toLowerCase() ===
                event.description.toLowerCase(),
            )
          ) {
            uniqueEvents.push(event);
          }
        });
      }

      return uniqueEvents;
    },
    [deliveriesData, proposalData?.newProposal, getCurrentDelivery],
  );

  useEffect(() => {
    if (deliveriesData) {
      const current = getCurrentDelivery({
        delivery: deliveriesData,
        isNew: proposalData?.newProposal,
      });
      const filteredEvents = current?.delivery
        ? getEventsFromDeliveriesData(current.delivery)
        : [];

      const uniqueEvents = replaceDuplicates(filteredEvents);

      console.log("uniqueEvents", uniqueEvents);
      const newEvents: EventType[] = [];
      uniqueEvents.forEach((event) => {
        newEvents.push(event);
        if (
          (event.description.toLowerCase().includes("coleta") &&
            !event.description.toLowerCase().includes("entrega")) ||
          (event.description.toLowerCase().includes("coletado") &&
            !event.description.toLowerCase().includes("entregue"))
        ) {
          const newEvent = {
            ...event,
            descricao: "Em trânsito",
          };
          newEvents.push(newEvent);
        }
      });

      setEvents(newEvents);
    }
  }, [
    deliveriesData,
    getEventsFromDeliveriesData,
    currentProductionSelected,
    replaceDuplicates,
    getCurrentDelivery,
    proposalData?.newProposal,
  ]);

  const newEventProduction = {
    description: "Aguardando coleta",
    eventDate: `${
      getCurrentProduction({
        production: productionData,
        isNew: proposalData?.newProposal,
      })?.awaitingCollectionDate
    }`,
    uf: null,
    cidade: null,
  };

  const newEventNotProduction = {
    description: "Aguardando coleta",
    eventDate: `${proposalData?.shipmentDate}`,
    uf: null,
    cidade: null,
  };

  let eventsWithNewEvent = events;

  if (!proposalData?.hasProduction) {
    eventsWithNewEvent = [newEventNotProduction, ...events];
  } else if (
    getCurrentProduction({
      production: productionData,
      isNew: proposalData?.newProposal,
    })?.awaitingCollectionDate
  ) {
    eventsWithNewEvent = [newEventProduction, ...events];
  }

  return {
    events: eventsWithNewEvent,
    scrollEventsRef,
  };
};

export default useDeliveriesDetails;
