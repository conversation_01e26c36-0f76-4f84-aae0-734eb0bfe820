import { useTheme } from "@/contexts/theme/themeContext";
import { useEffect, useRef, useState } from "react";

export const useHeader = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const { theme, toggleTheme } = useTheme();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
      setIsMenuOpen(false);
    }
  };

  const handleMouseOverOutside = (event: MouseEvent) => {
    if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
      setIsMenuOpen(false);
    }
  };

  useEffect(() => {
    if (isMenuOpen) {
      document.body.classList.add("overflow-hidden");
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("mouseover", handleMouseOverOutside);
    } else {
      document.body.classList.remove("overflow-hidden");
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("mouseover", handleMouseOverOutside);
    }
    return () => {
      document.body.classList.remove("overflow-hidden");
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("mouseover", handleMouseOverOutside);
    };
  }, [isMenuOpen]);

  return { isMenuOpen, menuRef, theme, toggleTheme, toggleMenu };
};
