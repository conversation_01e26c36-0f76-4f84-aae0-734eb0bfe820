import axios from "axios";
import { useEffect, useState } from "react";

interface IHoliday {
  date: string;
  name: string;
  type: string;
}

const IsWithinServiceHours = (
  hour: number,
  minutes: number,
  start: string,
  end: string,
) => {
  const [startHour, startMinutes] = start.split(":").map(Number);
  const [endHour, endMinutes] = end.split(":").map(Number);

  if (hour > startHour || (hour === startHour && minutes >= startMinutes)) {
    if (hour < endHour || (hour === endHour && minutes <= endMinutes)) {
      return true;
    }
  }
  return false;
};

const isBusinessDay = (weekDay: number) => {
  return weekDay >= 1 && weekDay <= 5;
};

export const fetchHolidaysByYear = async ({
  year,
}: {
  year: number;
}): Promise<IHoliday[]> => {
  const route = "https://brasilapi.com.br/api/feriados/v1";
  const response = await axios.get(`${route}/${year}`);
  return response.data;
};

export const useContact = ({
  homeService = "07:30",
  endService = "18:00",
}: {
  homeService: string;
  endService: string;
}) => {
  const [connectionStatus, setConnectionStatus] = useState("Offline");

  useEffect(() => {
    const verifyHour = async () => {
      const now = new Date();
      const hour = now.getHours();
      const minutes = now.getMinutes();
      const weekDay = now.getDay();
      const year = now.getFullYear();

      const holidays = await fetchHolidaysByYear({ year });
      const day = now.getDate();
      const month = now.getMonth() + 1;
      const todayEFeriado = holidays.some((holiday) => {
        const [holidayMonth, holidayDay] = holiday.date.split("-").map(Number);
        return holidayDay === day && holidayMonth === month;
      });

      const isTimeService = IsWithinServiceHours(
        hour,
        minutes,
        homeService,
        endService,
      );

      const isDayBusiness = isBusinessDay(weekDay);
      if (!todayEFeriado && isTimeService && isDayBusiness) {
        setConnectionStatus("Online");
      } else {
        setConnectionStatus("Offline");
      }
    };

    verifyHour();
  }, [homeService, endService]);

  return {
    connectionStatus,
    setConnectionStatus,
  };
};
