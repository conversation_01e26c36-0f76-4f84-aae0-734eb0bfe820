"use client";
import { removeCookies } from "@/lib/cookies/saveCookies";
import { logoutRequest } from "@/services/admin/requests/logout";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

export const useLogoutAdminMutation = () => {
  const router = useRouter();
  const { mutate } = useMutation({
    mutationKey: ["logout"],
    mutationFn: async () => {
      const clientId = await getFingerprint();
      const response = await logoutRequest({ clientId });

      if (response?.success) {
        toast.dismiss();
        toast.success("Logout efetuado com sucesso");
        removeCookies("tracker_admin_token");
        router.push("/admin");
      } else {
        removeCookies("tracker_admin_token");
        router.push("/admin");
      }

      return response;
    },
  });

  return {
    logout: mutate,
  };
};
