import { STATUSES } from "@/components/admin/dashboard/constants";
import { ITrackingProposal } from "@/services/admin/requests/tracking-find-all";
import { IApiInstance } from "@/types/utils";
import { useState } from "react";

const useModal = <T>() => {
  const [selectedItem, setSelectedItem] = useState<T | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (item: T) => {
    setSelectedItem(item);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setSelectedItem(null);
    setIsModalOpen(false);
  };

  return { selectedItem, isModalOpen, openModal, closeModal };
};

const groupProposalsByStatus = (
  proposals: ITrackingProposal[] = [],
): Record<string, ITrackingProposal[]> =>
  STATUSES.reduce<Record<string, ITrackingProposal[]>>((acc, status) => {
    acc[status.label] = proposals.filter(
      (proposal) => proposal.status === status.label,
    );
    return acc;
  }, {});

export const useDashboardItems = ({
  data,
  isLoading,
}: {
  data: IApiInstance<ITrackingProposal[]> | undefined;
  isLoading: boolean;
}) => {
  const proposals = data?.success ? data.data : [];
  const groupedData = groupProposalsByStatus(proposals);

  const statusesToRender = isLoading
    ? STATUSES
    : STATUSES.filter(({ label }) => groupedData[label].length > 0);

  const {
    selectedItem: selectedTracking,
    isModalOpen,
    openModal,
    closeModal,
  } = useModal<ITrackingProposal>();

  return {
    groupedData,
    statusesToRender,
    selectedTracking,
    isModalOpen,
    openModal,
    closeModal,
  };
};
