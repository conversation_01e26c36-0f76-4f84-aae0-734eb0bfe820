import { generateTrackingLinkRequest } from "@/services/admin/requests/generate-tracking-link";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

export const useGenerateNewTrackingLink = () => {
  const { mutate } = useMutation({
    mutationKey: ["generate-new-tracking-link"],
    mutationFn: async ({ id }: { id: number }) => {
      const clientId = await getFingerprint();
      const hash = await generateTrackingLinkRequest({ id: id, clientId });
      if (!hash?.success) {
        toast.dismiss();
        toast.error(hash?.data.message);
      } else {
        window.open(
          "http://localhost:3000/dashboard/code?" + hash?.data.hash,
          "_blank",
        );
      }
    },
  });

  return {
    generateNewTrackingLink: mutate,
  };
};
