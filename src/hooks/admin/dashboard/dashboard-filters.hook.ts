import { StatusEnum } from "@/components/admin/dashboard/constants";
import { getStatusIdWithoutString } from "@/lib/admin/dashboard/status-utils";
import { StatusStringType } from "@/services/admin/requests/tracking-find-all";
import { useCallback, useState } from "react";

export interface DashboardFilterSetters {
  setStatus: (status: StatusEnum | undefined) => void;
  setType: (type: "em andamento" | "finalizado" | undefined) => void;
  setDate: (date: Date | undefined) => void;
}

const mapTypeValue = (
  value: string,
): "em andamento" | "finalizado" | undefined => {
  switch (value) {
    case "em-andamento":
      return "em andamento";
    case "finalizado":
      return "finalizado";
    case "todos":
    default:
      return undefined;
  }
};

export const useDashboardFilters = ({
  setStatus,
  setType,
  setDate,
}: DashboardFilterSetters) => {
  const [isCalendarDateOpen, setCalendarDateOpen] = useState(false);

  const handleStatusChange = useCallback(
    (value: string) => {
      const statusId = getStatusIdWithoutString(value as StatusStringType);
      setStatus(statusId);
    },
    [setStatus],
  );

  const handleTypeChange = useCallback(
    (value: string) => {
      setType(mapTypeValue(value));
    },
    [setType],
  );

  const handleDateSelect = useCallback(
    (selected?: Date) => {
      if (selected) {
        setDate(selected);
        setCalendarDateOpen(false);
      }
    },
    [setDate],
  );

  return {
    isCalendarDateOpen,
    setCalendarDateOpen,
    handleStatusChange,
    handleTypeChange,
    handleDateSelect,
  };
};
