type StatusMapping = {
  id: number;
  label: string;
};

const STATUS_MAPPINGS: StatusMapping[] = [
  { id: 1, label: "Aprovado" },
  { id: 2, label: "Em produção" },
  { id: 3, label: "Manufaturado" },
  { id: 4, label: "Expedido" },
  { id: 5, label: "Em trânsito" },
  { id: 6, label: "Entregue" },
];

export const getStatusIdWithoutString = (
  status: string,
): number | undefined => {
  const mapping = STATUS_MAPPINGS.find(
    (item) => item.label.toLowerCase() === status.toLowerCase(),
  );
  return mapping?.id;
};

export const getStatusStringWithoutStatusId = (statusId: number): string => {
  const mapping = STATUS_MAPPINGS.find((item) => item.id === statusId);
  return mapping ? mapping.label : "Todos";
};
