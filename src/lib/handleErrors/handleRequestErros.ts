import { MESSAGE_REFRESH_ERROR } from "@/services/api/apiInstance";
import { IResponseError } from "@/types/utils";
import { AxiosError } from "axios";

export type ErrorData = {
  message: string;
  status: number;
  url?: string;
  method?: string;
  errorRefreshLogout?: boolean;
};

export const handleGlobalErrors = (
  error: AxiosError | unknown,
): IResponseError => {
  if (
    error instanceof Object &&
    (error as { message: string }).message ===
      "Sessão admin expirada. Por favor, faça login novamente."
  ) {
    return {
      success: false,
      data: {
        message: "Sessão admin expirada",
        status: 401,
        errorRefreshLogout: true,
      },
      status: 401,
    };
  }

  if (error instanceof Error && error.message === MESSAGE_REFRESH_ERROR) {
    return {
      success: false,
      data: {
        message: "Erro ao fazer o refresh",
        status: 0,
        errorRefreshLogout: true,
      },
      status: 0,
    };
  }

  if (error instanceof AxiosError) {
    if (error.response) {
      const { status, data, config } = error.response;
      if (typeof data === "string" && data.startsWith("<!DOCTYPE html>")) {
        return {
          success: false,
          data: {
            message: "Erro de conexão",
            status,
            url: config.url,
            method: config.method,
          },
          status,
        };
      }

      const message =
        data?.message ||
        (typeof data === "string" ? data : "Erro desconhecido");
      return {
        success: false,
        data: {
          message,
          status,
          url: config.url,
          method: config.method,
        },
        status,
      };
    }

    if (error.request) {
      const { url, method } = error.config as { url: string; method: string };
      return {
        success: false,
        data: {
          message: error.message || "O servidor não respondeu à solicitação.",
          status: 0,
          url,
          method,
        },
        status: 0,
      };
    }
  } else if (error instanceof Error) {
    return {
      success: false,
      data: {
        message: error.message,
        status: 0,
      },
      status: 0,
    };
  }

  return {
    success: false,
    data: {
      message: "Erro desconhecido",
      status: 0,
    },
    status: 0,
  };
};
