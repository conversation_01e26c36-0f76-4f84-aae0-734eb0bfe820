"use server";

import { cookies } from "next/headers";
import { getJWTPayloadProperty } from "../jwt/get-jwt-payload-property";

export const getNewProperty = async (): Promise<{
  message: string;
  status: boolean;
}> => {
  const cookieStore = await cookies();
  const token = cookieStore.get("access_token")?.value;
  const isNew = getJWTPayloadProperty<boolean>(token ?? "", "new");

  if (isNew) {
    return {
      message: "Esse pedido é um pedido novo",
      status: true,
    };
  } else {
    return {
      message: "Esse pedido é um pedido antigo",
      status: false,
    };
  }
};
