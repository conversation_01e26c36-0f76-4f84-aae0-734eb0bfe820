"use server";

import { getCookies, removeCookies } from "@/lib/cookies/saveCookies";
import {
  ErrorData,
  handleGlobalErrors,
} from "@/lib/handleErrors/handleRequestErros";
import { apiLivre } from "@/services/api/apiInstance";
import { IResponse } from "@/types/utils";

interface LogoutResponse {
  success: boolean;
}

const logoutRequest = async (
  id: string,
): Promise<IResponse<boolean | ErrorData>> => {
  try {
    const refreshToken = await getCookies("refresh_token");
    const { status } = await apiLivre.post<LogoutResponse>(
      "/auth/logout",
      {},
      {
        headers: {
          "X-Client-ID": id,
          Authorization: `Bearer ${refreshToken}`,
        },
      },
    );
    if (status === 200) {
      await removeCookies("refresh_token");
      await removeCookies("access_token");
    }
    return {
      success: true,
      status,
      data: true,
    };
  } catch (error: unknown) {
    return handleGlobalErrors(error);
  }
};

export default logoutRequest;
