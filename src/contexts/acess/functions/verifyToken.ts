"use server";

import { getCookies } from "@/lib/cookies/saveCookies";

export const VerifyToken = async (): Promise<boolean> => {
  try {
    const token: string | null = await getCookies("access_token");
    const refreshToken: string | null = await getCookies("refresh_token");
    if (
      (token && token.trim() !== "") ||
      (refreshToken && refreshToken.trim() !== "")
    ) {
      return true;
    }
    return false;
  } catch (error) {
    // console.error("Error verifying token:", error);
    return false;
  }
};
