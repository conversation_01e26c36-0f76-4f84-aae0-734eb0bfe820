"use client";
import { DetailsProductContextProps } from "@/types/context/detailsContext";
import { createContext, useContext, useMemo, useRef, useState } from "react";

const detailsProductContext = createContext<DetailsProductContextProps | null>(
  null,
);

export const DetailsProductProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [currentProductType, setCurrentProductType] = useState<string | null>(
    null,
  );
  const [changeProductTypeButton, setChangeProductTypeButton] = useState<
    string | null
  >(null);
  const [animationKeyDelivery, setAnimationKeyDelivery] = useState<number>(0);
  const [animationKeyProduction, setAnimationKeyProduction] =
    useState<number>(0);
  const scrollEventsRef = useRef<HTMLDivElement>(null);
  const [isWhatsappOpen, setIsWhatsappOpen] = useState(false);

  const DetailsValue = useMemo(
    () => ({
      currentProductType,
      setCurrentProductType,
      scrollEventsRef,
      changeProductTypeButton,
      setChangeProductTypeButton,
      animationKeyDelivery,
      animationKeyProduction,
      setAnimationKeyDelivery,
      setAnimationKeyProduction,
      setIsWhatsappOpen,
      isWhatsappOpen,
    }),
    [
      currentProductType,
      scrollEventsRef,
      changeProductTypeButton,
      animationKeyDelivery,
      animationKeyProduction,
      isWhatsappOpen,
    ],
  );

  return (
    <detailsProductContext.Provider value={DetailsValue}>
      {children}
    </detailsProductContext.Provider>
  );
};

export const useDetailsProductContext = () => {
  const context = useContext(detailsProductContext);
  if (context === null) {
    throw new Error(
      "useDetailsProductContext deve ser usado dentro de um DetailsProductProvider",
    );
  }
  return context;
};

export default detailsProductContext;
