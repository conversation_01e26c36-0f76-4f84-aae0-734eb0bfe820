"use client";
import { ToastLoadingFetch } from "@/components/LoadingFetch";
import { useCurrentProduction } from "@/hooks/productionProgress/useCurrentProduction";
import { ErrorData } from "@/lib/handleErrors/handleRequestErros";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { IDataContext } from "@/types/context/dataContext";
import { IResponse } from "@/types/utils";
import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { createContext, useContext, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useAcessContext } from "../acess/acessContext";
import { fetchHash } from "../acess/functions/fetchHash";
import { useDeliveries } from "./queries/delivery";
import { useProduction } from "./queries/production";
import { useProposal } from "./queries/proposal";

const dataContext = createContext<IDataContext | null>(null);

export const DataProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const { logout, currentProductionSelected, setCurrentProductionSelected } =
    useAcessContext();
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [openNotificationHash, setOpenNotificationHash] =
    useState<boolean>(false);
  const { getCurrentProduction } = useCurrentProduction();

  const {
    isAuthenticated,
    fetchToDeliveries,
    setFetchToDeliveries,
    setHashExpired,
  } = useAcessContext();
  const {
    proposalData,
    proposalSuccess,
    proposalLoading,
    proposalError,
    proposalRefetch,
  } = useProposal(isAuthenticated, logout);
  const {
    productionData,
    productionSuccess,
    productionLoading,
    productionError,
    productionRefetch,
    productionFetched,
  } = useProduction(isAuthenticated, setCurrentProductionSelected);
  const {
    deliveriesData,
    deliveriesSuccess,
    deliveriesFetched,
    deliveriesLoading,
    deliveriesError,
    deliveriesRefetch,
  } = useDeliveries(fetchToDeliveries);

  useEffect(() => {
    if (!proposalData?.status) return;
    const stage = proposalData?.status;

    //

    // const currentProduction = productionData?.find(
    //   (item) => item.order === currentProductionSelected,
    // );

    if (
      proposalData?.newProposal &&
      getCurrentProduction({
        production: productionData,
        isNew: proposalData?.newProposal,
      })?.awaitingCollectionDate
    ) {
      setFetchToDeliveries(true);
    }

    if (
      proposalData.shipmentDate &&
      proposalData.carrier !== "Retirar Na Fábrica"
    ) {
      setFetchToDeliveries(true);
    }

    switch (stage) {
      case "Em Análise Financeira":
        setFetchToDeliveries(false);
        break;
      case "Expedido":
        if (proposalData.carrier !== "Retirar Na Fábrica") {
          setFetchToDeliveries(true);
        }
        break;
    }
  }, [
    proposalData,
    setFetchToDeliveries,
    currentProductionSelected,
    productionData,
    setCurrentProductionSelected,
  ]);

  const hashMutation = useMutation({
    mutationKey: ["hash"],
    mutationFn: async (hash: string) => {
      const id = await getFingerprint();
      return fetchHash(hash, id);
    },
    onSuccess: (response: IResponse<unknown>) => {
      if (response.success) {
        router.push("/dashboard");
      } else {
        const data = response.data as ErrorData;

        if (data.status === 429) {
          toast.dismiss();
          toast.error(data.message);
          router.push("/access-page");

          return;
        }

        if (data.status === 401) {
          setHashExpired(true);
          router.push("/access-page");
          return;
        }

        if (data.errorRefreshLogout) {
          setHashExpired(true);
          router.push("/access-page");
        } else {
          toast.error(data.message);
          router.push("/access-page");
        }
      }
    },
  });

  useEffect(() => {
    if (proposalLoading) {
      setToastMessage("Buscando Proposta...");
    } else if (deliveriesLoading) {
      setToastMessage("Buscando Entrega...");
    } else if (productionLoading) {
      setToastMessage("Buscando Produção...");
    } else {
      setToastMessage(null);
    }
  }, [proposalLoading, deliveriesLoading, productionLoading]);

  useEffect(() => {
    const url = window.location.href;
    const hash = url.split("?")[1];

    const notification = url.split("?")[2];
    if (notification && notification.includes("notification=true"))
      setOpenNotificationHash(true);
    if (hash) hashMutation.mutate(hash);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const contextValue = {
    proposalData,
    proposalSuccess,
    proposalLoading,
    proposalError,
    proposalRefetch,
    productionData,
    productionSuccess,
    productionLoading,
    productionError,
    productionRefetch,
    deliveriesData,
    deliveriesSuccess,
    deliveriesLoading,
    deliveriesError,
    deliveriesFetched,
    deliveriesRefetch,
    productionFetched,
    setToastMessage,
    toastMessage,
    openNotificationHash,
    setOpenNotificationHash,
  };

  return (
    <dataContext.Provider value={contextValue}>
      {children}
      {toastMessage && <ToastLoadingFetch message={toastMessage} />}
    </dataContext.Provider>
  );
};

export const useDataContext = () => {
  const context = useContext(dataContext);
  if (context === null) {
    throw new Error("useDataContext must be used within an DataProvider");
  }
  return context;
};
