"use client";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { ProductionRequest } from "@/types/production";
import toast from "react-hot-toast";
import { productionRequest } from "./productionRequest";

export const fetchProduction = async (): Promise<
  ProductionRequest[] | null
> => {
  toast.dismiss();

  const id = await getFingerprint();
  const data = await productionRequest(id);
  if (data.success) {
    return data.data;
  } else {
    // toast.error(data.data.message);
    throw new Error(data.data.message);
  }
};
