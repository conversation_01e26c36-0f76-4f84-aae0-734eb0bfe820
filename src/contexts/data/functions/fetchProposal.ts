"use client";
import { ErrorData } from "@/lib/handleErrors/handleRequestErros";
import { getFingerprint } from "@/services/visitorId/getVisitorId";
import { ProposalDataInterface } from "@/types/proposal";
import toast from "react-hot-toast";
import { proposalRequest } from "./proposalRequest";

export const fetchProposal = async ({
  onErrorLogout,
}: {
  onErrorLogout: () => void;
}) => {
  const id = await getFingerprint();
  const { data, success } = await proposalRequest(id);
  if (success) {
    return data as ProposalDataInterface;
  } else {
    const error = data as ErrorData;
    if (error.errorRefreshLogout) {
      onErrorLogout();
      return;
    }

    if (error.message !== "socket hang up") {
      toast.error(error.message);
      onErrorLogout();
      return;
    }

    if (error.message === "socket hang up") {
      toast(
        "O servidor está demorando para responder, aguarde alguns segundos",
        {
          duration: 4000,
        },
      );
    }
    throw new Error(error.message);
  }
};
