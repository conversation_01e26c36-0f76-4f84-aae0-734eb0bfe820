"use server";
import { handleGlobalErrors } from "@/lib/handleErrors/handleRequestErros";
import { apiInstance } from "@/services/api/apiInstance";
import { ProductionRequest } from "@/types/production";
import { IResponseError, IResponseSuccess } from "@/types/utils";

export const productionRequest = async (
  id: string,
): Promise<IResponseSuccess<ProductionRequest[]> | IResponseError> => {
  try {
    const { data, status } = await apiInstance.get<ProductionRequest[]>(
      "/production/find",
      {
        headers: {
          "X-Client-ID": id,
        },
      },
    );
    return { success: true, data, status: status };
  } catch (error) {
    return handleGlobalErrors(error);
  }
};
