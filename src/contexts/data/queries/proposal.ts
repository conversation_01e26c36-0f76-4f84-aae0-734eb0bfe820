"use client";
import { ProposalDataInterface } from "@/types/proposal";
import { useQuery } from "@tanstack/react-query";
import { fetchProposal } from "../functions/fetchProposal";

export const useProposal = (isAuthenticated: boolean, logout: () => void) => {
  const {
    data: proposalData,
    isSuccess: proposalSuccess,
    isLoading: proposalLoading,
    error: proposalError,
    refetch: proposalRefetch,
  } = useQuery<ProposalDataInterface | null | undefined>({
    enabled: isAuthenticated,
    queryKey: ["proposal"],
    queryFn: () => fetchProposal({ onErrorLogout: logout }),
    retry: 3,
  });

  return {
    proposalData,
    proposalSuccess,
    proposalLoading,
    proposalError,
    proposalRefetch,
  };
};
