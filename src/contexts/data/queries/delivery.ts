"use client";
import { DeliveryInterface } from "@/types/deliveries";
import { useQuery } from "@tanstack/react-query";
import { fetchDeliveries } from "../functions/fetchDeliveries";

export const useDeliveries = (fetchToDeliveries: boolean) => {
  const {
    data: deliveriesData,
    isSuccess: deliveriesSuccess,
    isLoading: deliveriesLoading,
    error: deliveriesError,
    refetch: deliveriesRefetch,
    isFetched: deliveriesFetched,
  } = useQuery<DeliveryInterface[] | null>({
    enabled: fetchToDeliveries,
    retry: false,
    queryKey: ["deliveries"],
    queryFn: () => fetchDeliveries(),
  });

  return {
    deliveriesData,
    deliveriesFetched,
    deliveriesSuccess,
    deliveriesLoading,
    deliveriesError,
    deliveriesRefetch,
  };
};
