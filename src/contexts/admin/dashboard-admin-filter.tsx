import { StatusEnum } from "@/components/admin/dashboard/constants";
import React, { createContext, useContext, useState } from "react";

export interface IDashboardFilters {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  status: StatusEnum | undefined;
  setStatus: (status: StatusEnum | undefined) => void;
  type: "em andamento" | "finalizado" | undefined;
  setType: (type: "em andamento" | "finalizado" | undefined) => void;
  proposalNumber: string | undefined;
  setProposalNumber: (proposalNumber: string | undefined) => void;
  itemsLimitPerPage: number;
  setItemsLimitPerPage: (itemsLimitPerPage: number) => void;
  resetFilters: () => void;
  username: string | undefined;
  setUsername: (username: string | undefined) => void;
}

const DashboardAdminFilterContext = createContext<IDashboardFilters | null>(
  null,
);

export const useDashboardAdminFilterContext = () => {
  const context = useContext(DashboardAdminFilterContext);
  if (!context) {
    throw new Error(
      "useDashboardAdminFilterContext must be used within a DashboardAdminFilterProvider",
    );
  }
  return context;
};

export const DashboardAdminFilterProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [username, setUsername] = useState<string | undefined>(undefined);
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [status, setStatus] = useState<StatusEnum | undefined>(undefined);
  const [type, setType] = useState<"em andamento" | "finalizado" | undefined>(
    undefined,
  );
  const [proposalNumber, setProposalNumber] = useState<string | undefined>(
    undefined,
  );
  const [itemsLimitPerPage, setItemsLimitPerPage] = useState<number>(50);

  const resetFilters = () => {
    setDate(undefined);
    setStatus(undefined);
    setType(undefined);
    setProposalNumber(undefined);
    setItemsLimitPerPage(50);
  };

  return (
    <DashboardAdminFilterContext.Provider
      value={{
        date,
        setDate,
        status,
        setStatus,
        type,
        setType,
        proposalNumber,
        setProposalNumber,
        itemsLimitPerPage,
        setItemsLimitPerPage,
        resetFilters,
        setUsername,
        username,
      }}
    >
      {children}
    </DashboardAdminFilterContext.Provider>
  );
};
