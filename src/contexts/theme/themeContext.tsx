"use client";
import { getThemeCookie, setThemeCookie } from "@/lib/cookies/themeCookies";
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

export type Theme = "light" | "dark";

interface ThemeContextProps {
  theme: Theme | null;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextProps | undefined>(undefined);

const getSystemTheme = (): Theme => {
  if (
    window.matchMedia &&
    window.matchMedia("(prefers-color-scheme: dark)").matches
  ) {
    return "dark";
  }
  return "light";
};

const checkThemeConsent = (): boolean => {
  const cookieConsent = localStorage.getItem("cookieConsent");
  if (!cookieConsent) return false;

  try {
    const settings = JSON.parse(cookieConsent);
    return settings.theme === true;
  } catch {
    return false;
  }
};

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [theme, setTheme] = useState<Theme | null>(null);
  const themeContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const hasThemeConsent = checkThemeConsent();
    if (hasThemeConsent) {
      const savedTheme = getThemeCookie();
      if (savedTheme) {
        setTheme(savedTheme);
        return;
      }
    }

    const systemTheme = getSystemTheme();
    setTheme(systemTheme);

    if (hasThemeConsent) {
      setThemeCookie(systemTheme);
    }
  }, []);

  useEffect(() => {
    if (theme) {
      document.documentElement.setAttribute("data-theme", theme);
      if (checkThemeConsent()) {
        setThemeCookie(theme);
      }
    }
  }, [theme]);

  const toggleTheme = useCallback(() => {
    if (themeContainerRef.current) {
      themeContainerRef.current.classList.remove("theme-transition");
      themeContainerRef.current.classList.add("theme-transition");
    }

    setTheme((prevTheme) => (prevTheme === "light" ? "dark" : "light"));

    setTimeout(() => {
      if (themeContainerRef.current) {
        themeContainerRef.current.classList.remove("theme-transition");
      }
    }, 1000);
  }, []);

  const value = useMemo(() => ({ theme, toggleTheme }), [theme, toggleTheme]);

  if (theme === null) {
    return null;
  }

  return (
    <ThemeContext.Provider value={value}>
      <div className="theme-container" ref={themeContainerRef}>
        {children}
      </div>
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextProps => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("O useTheme deve ser usado dentro do ThemeProvider");
  }
  return context;
};
