import { NextRequest } from "next/server";

const BLOCKED_AGENTS = [
  // CLI Tools
  "curl",
  "wget",
  "httpie",
  "lwp-request",
  "fetch libfetch",

  // Ferramentas e bibliotecas HTTP populares
  "python-requests",
  "axios",
  "node-fetch",
  "go-http-client",
  "java-http-client",
  "okhttp",
  "urlgrabber",
  "libwww-perl",

  // Ferramentas/API Clients
  "postman",
  "insomnia",
  "paw",
  "restsharp",
  "advanced rest client",
  // adionar o thunder com thunder-client
  "thunder client",

  // Scrapers conhecidos
  "scrapy",
  "httpclient",
  "webcopy",
  "httrack",
  "site sucker",
  "teleportpro",

  // Bots genéricos
  "bot",
  "crawler",
  "spider",
  "scan",
  "extract",
  "scraper",
  "datanyze",
  "feedfetcher",
  "seznambot",
  "yandex",
  "baiduspider",
  "facebookexternalhit",
  "linkchecker",
  "slurp",
  "sogou",
  "duckduckbot",
  "mediapartners-google",
  "googlebot",
  "bingbot",
  "embedly",
  "phantomjs",

  // Outros
  "masscan",
  "zgrab",
  "sqlmap",
  "nikto",
  "fuzz",
];

export const isCurlUserAgent = (req: NextRequest) => {
  const userAgent = req.headers.get("user-agent")?.toLowerCase() ?? "";
  return BLOCKED_AGENTS.some((agent) => userAgent.includes(agent));
};
