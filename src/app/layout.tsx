import Providers from "@/providers";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import type { Metadata } from "next";
import { Montserrat } from "next/font/google";
import "./globals.css";

const inter = Montserrat({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Pormade Tracker",
  description:
    "Plataforma de Rastreamento: Acompanhe a jornada completa de seus produtos, desde a fabricação até a entrega final. Visualize cada etapa, setor e local por onde seus itens passam, garantindo total transparência e controle ao longo do percurso até suas mãos.",
  keywords:
    "Pormade, Pormade Tracker, rastreamento, plataforma de rastreamento, acompanhamento de pedidos",
  icons: "/icon.png",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <head>
        <link rel="icon" href="/icon.png" type="image/png" />
      </head>
      <body className={inter.className}>
        <Providers>
          {children}
          {process.env.NODE_ENV !== "production" && <ReactQueryDevtools />}
        </Providers>
        <div id="toast-fetch-root"></div>
        <div id="popup"></div>
        <div id="notification-config"></div>
        <div id="warning-transport-from-another"></div>
      </body>
    </html>
  );
}
