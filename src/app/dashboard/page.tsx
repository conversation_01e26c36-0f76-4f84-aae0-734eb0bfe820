"use server";
import { Contact } from "@/components/ contact";
import { CookieConsent } from "@/components/cookieConsent";
import { DetailsProposalTable } from "@/components/detailsProposalTable";
import Footer from "@/components/footer";
import Header from "@/components/header";
import InitialInformation from "@/components/initialInformation";
import { NewMap } from "@/components/map";
import SliderComponent from "@/components/slidesComponents";
import { Toaster } from "react-hot-toast";

// const Map = dynamic(() => import("@/components/map"), { ssr: false });

const DashboardPage = () => {
  return (
    <main className={`flex flex-col min-h-screen justify-between`}>
      <Header />
      <section className={`flex-1 h-1/2 bg-PormadeGray`}>
        <div className={`h-full w-full p-4 flex items-center`}>
          <div className={`flex-1 mb-4 md:mb-12 mt-3`}>
            <InitialInformation />
          </div>
        </div>
      </section>
      <section
        className={`flex-1 h-auto md:h-1/2 gap-4 min-h-[400px] bg-bgPrimaryWhite w-full`}
      >
        <div
          className={`flex p-4 justify-center flex-col lg:flex-row lg:mr-0 md:mr-0 mt-2 lg:gap-4`}
        >
          <div className={`w-full flex-1 lg:w-2/3`}>
            <DetailsProposalTable />
          </div>
          <div className={`w-full mt-8 lg:mt-0 lg:w-1/3`}>
            <SliderComponent />
          </div>
        </div>
      </section>
      <NewMap />
      <Contact homeService="07:30" endService="18:00" />
      <Footer />
      <CookieConsent />

      <Toaster position="top-right" reverseOrder={false} />
    </main>
  );
};

export default DashboardPage;
