import AccessForm from "@/components/accessPage";
import { LogoAccess } from "@/components/accessPage/logo";
import { MapImage } from "@/components/accessPage/mapImage";
import { ThemeButton } from "@/components/accessPage/themeButton";
import { Toaster } from "react-hot-toast";

const AccessPage = () => {
  return (
    <main className="flex min-h-screen h-screen bg-PormadeGray">
      <aside className="hidden md:flex md:w-1/2 h-full justify-center items-center">
        <div className="h-full w-full bg-PormadeGreen flex justify-center items-center rounded-br-[15rem]">
          <MapImage />
        </div>
      </aside>
      <div className="flex w-full md:w-1/2 justify-center items-center p-6">
        <article className="sm:max-w-[38rem] mx-w-[250px] h-auto min-h-[32rem] w-full p-6 bg-bgPrimaryWhite rounded-lg shadow-md flex flex-col justify-center">
          <header className="w-full h-16 tl480:h-24 flex justify-center items-center">
            <LogoAccess />
          </header>
          <section className="text-center flex flex-col justify-center items-center tl480:p-4">
            <p className="text-textColorPrimary text-xs tl480:text-sm mb-2 max-h-16">
              Bem-vindo ao Pormade Tracker, o nosso sistema de rastreamento de
              pedidos! Para visualizar os detalhes do seu pedido, basta inserir
              o seu CPF e o número do pedido nos campos abaixo.
            </p>
          </section>
          <AccessForm />
        </article>
        <ThemeButton />
      </div>
      <Toaster position="top-right" reverseOrder={false} />
    </main>
  );
};

export default AccessPage;
