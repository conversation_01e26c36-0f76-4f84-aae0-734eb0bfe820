@tailwind base;
@tailwind components;
@tailwind utilities;

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px var(--bg-gray-input-login) inset !important;
  box-shadow: 0 0 0px 1000px var(--bg-gray-input-login) inset !important;
  background-color: var(--bg-gray-input-login) !important;
  color: var(--text-input-login) !important;
  border-bottom: 1px solid black;
  caret-color: inherit !important;
  -webkit-text-fill-color: black !important;
  transition:
    background-color 0.3s linear,
    color 0.3s ease-in-out;
}

[data-theme="dark"] input:-webkit-autofill,
[data-theme="dark"] input:-webkit-autofill:hover,
[data-theme="dark"] input:-webkit-autofill:focus,
[data-theme="dark"] textarea:-webkit-autofill,
[data-theme="dark"] textarea:-webkit-autofill:hover,
[data-theme="dark"] textarea:-webkit-autofill:focus,
[data-theme="dark"] select:-webkit-autofill,
[data-theme="dark"] select:-webkit-autofill:hover,
[data-theme="dark"] select:-webkit-autofill:focus {
  -webkit-box-shadow: 0 0 0px 1000px var(--bg-gray-input-login) inset !important;
  box-shadow: 0 0 0px 1000px var(--bg-gray-input-login) inset !important;
  background-color: var(--bg-gray-input-login) !important;
  color: var(--text-input-login) !important;
  border-bottom: 1px solid white;
  caret-color: inherit !important;
  -webkit-text-fill-color: white !important;
  transition:
    background-color 0.3s linear,
    color 0.3s ease-in-out;
}

:root {
  --svg-color-primary: #a0ffc0;
  --svg-color-secondary: #52f68a;
  --svg-color-accent: #ffffff;
  --svg-color-dark: #000000;

  --svg-bg-var: #aaaaaa;

  --map-control-button: #fff;
  --map-control-icon: #666666;
  --theme-transition-bg-start: rgba(0, 0, 0);
  --theme-transition-bg-end: rgba(215, 215, 215);
  --theme-transition-shadow: 0 0 50px 30px rgba(0, 0, 0, 0.6);
  --primary-color-green: #079d3a;
  --bg-primary-white: #ffffff;
  --text-color-primary: #000000;
  --bg-pormade-gray: #d9d9d9;
  --bg-form-login: #f5f5f5;
  --text-gray-login: #6b7280;
  --text-input-login: rgb(17 24 39);
  --bg-gray-input-login: rgb(229 231 235);
  --text-button-login: #ffffff;
  --bg-button-login-hover: #0c811f;
  --shadow-button-login: rgba(124, 124, 124, 1);
  --label-input-login-active: rgb(55 65 81);
  --label-input-login-inactive: rgb(31 41 55);

  /* initital information */
  --bg-order-number-loading: rgb(209 213 219);
  --text-gray-inititalInformation: rgb(31 41 55);
  --bg-progress-timeline: rgb(209 213 219);
  --bg-green-progress-timeline: rgb(21 128 61);
  --circle-progress-timeline: #9ca3af;
  --circle-progress: #e5e7eb;
  --text-data-inactive-progress: #9ca3af;
  --text-stage-inactive: #374151;

  /* details Proposal table */

  --shadow-color-card: rgba(0, 0, 0, 0.3);
  --text-title-card: #fff;
  --shadow-text-title-card: #000;
  --bg-card-details-proposal: #fff;
  --border-items-details-proposal: rgb(209 213 219);
  --text-button-color: #fff;
  --text-button-bg-active: rgb(20 83 45);
  --border-details-proposal: rgb(229 231 235);
  --text-obs-image: rgb(107 114 128);
  ---bg-button-details: #4f4f4f;

  /* delivery details table */
  --bg-items-table-delivery: rgb(243 244 246);
  --border-items-table: rgb(209 213 219);

  --bg-loading1: rgb(209 213 219);
  --bg-loading2: rgb(209 213 219);

  /*footer */

  --border-copy: rgb(156 163 175);
  --text-copy: rgb(107 114 128);
}

[data-theme="dark"] {
  --svg-color-primary: #05632a;
  --svg-color-secondary: #079d3a;
  --svg-color-accent: #ffffff;
  --svg-color-dark: #eaeaea;

  --svg-bg-var: #ffffff;

  --map-control-button: #444444;
  --map-control-icon: #b3b3b3;

  --theme-transition-bg-start: rgba(255, 255, 255);
  --theme-transition-bg-end: rgba(0, 0, 0);
  --theme-transition-shadow: 0 0 50px 30px rgba(255, 255, 255, 0.6);
  --primary-color-green: #05632a;
  --bg-primary-white: #141414;
  --text-color-primary: #eaeaea;
  --bg-pormade-gray: #1e1e1e;
  --bg-form-login: #262626;
  --text-gray-login: #b0b0b0;
  --text-input-login: #d8d8d8;
  --bg-gray-input-login: #1c1c1c;
  --text-button-login: #ffffff;
  --bg-button-login-hover: #064620;
  --shadow-button-login: rgba(0, 0, 0, 0.7);
  --label-input-login-active: #c7c7c7;
  --label-input-login-inactive: #929292;

  /* Initial Information */
  --bg-order-number-loading: #3b3b3b;
  --text-gray-inititalInformation: #c7c7c7;
  --bg-progress-timeline: #2d2c2c;
  --bg-green-progress-timeline: #136f3c;
  --circle-progress-timeline: #555658;
  --circle-progress: #7e8085;
  --text-data-inactive-progress: #bdc0c5;
  --text-stage-inactive: #696f79;

  /* Details Proposal Table */
  --shadow-color-card: rgba(126, 126, 126, 0.3);
  --text-title-card: #ffffff;
  --shadow-text-title-card: #000000;
  --bg-card-details-proposal: #1b1b1b;
  --border-items-details-proposal: #3b3b3b;
  --text-button-color: #ffffff;
  --text-button-bg-active: #0d4226;
  --border-details-proposal: #3b3b3b;
  --text-obs-image: #929292;
  --bg-button-details: #4f4f4f;

  /* Delivery Details Table */
  --bg-items-table-delivery: #292929;
  --border-items-table: #3b3b3b;

  --bg-loading1: rgb(45, 47, 49);
  --bg-loading2: rgb(100, 102, 105);

  /* Footer */
  --border-copy: #4a4a4a;
  --text-copy: #929292;
}

[data-theme="light"] {
  --svg-color-primary: #a0ffc0;
  --svg-color-secondary: #52f68a;
  --svg-color-accent: #ffffff;
  --svg-color-dark: #000000;

  --svg-bg-var: #aaaaaa;
  --map-control-button: #fff;
  --map-control-icon: #666666;
  --theme-transition-bg-start: rgba(0, 0, 0);
  --theme-transition-bg-end: rgba(215, 215, 215);
  --primary-color-green: #079d3a;
  --bg-primary-white: #ffffff;
  --text-color-primary: #000000;
  --bg-pormade-gray: #d9d9d9;
  --bg-form-login: #f5f5f5;
  --text-gray-login: #6b7280;
  --text-input-login: rgb(17 24 39);
  --bg-gray-input-login: rgb(229 231 235);
  --text-button-login: #ffffff;
  --bg-button-login-hover: #0c811f;
  --shadow-button-login: rgba(124, 124, 124, 1);
  --label-input-login-active: rgb(55 65 81);
  --label-input-login-inactive: rgb(31 41 55);

  /* initital information */
  --bg-order-number-loading: rgb(209 213 219);
  --text-gray-inititalInformation: rgb(31 41 55);
  --bg-progress-timeline: rgb(209 213 219);
  --bg-green-progress-timeline: rgb(21 128 61);
  --circle-progress-timeline: #9ca3af;
  --circle-progress: #e5e7eb;
  --text-data-inactive-progress: #9ca3af;
  --text-stage-inactive: #686b6e;

  /* details Proposal table */

  --shadow-color-card: rgba(0, 0, 0, 0.3);
  --text-title-card: #fff;
  --shadow-text-title-card: #000;
  --bg-card-details-proposal: #fff;
  --border-items-details-proposal: rgb(209 213 219);
  --text-button-color: #fff;
  --text-button-bg-active: rgb(20 83 45);
  --border-details-proposal: rgb(229 231 235);
  --text-obs-image: rgb(107 114 128);
  --bg-button-details: rgb(209 213 219);

  /* delivery details table */
  --bg-items-table-delivery: rgb(243 244 246);
  --border-items-table: rgb(209 213 219);

  --bg-loading1: rgb(209 213 219);
  --bg-loading2: rgb(209 213 219);

  /*footer */

  --border-copy: rgb(156 163 175);
  --text-copy: rgb(107 114 128);
}

@keyframes themeTransition {
  0% {
    clip-path: circle(0% at 100% 0%);
    background: var(--theme-transition-bg-start);
    box-shadow: var(--theme-transition-shadow);
  }
  50% {
    clip-path: circle(75% at 50% 50%);
    background: var(--theme-transition-bg-start);
    box-shadow: 0 0 30px 20px rgba(0, 0, 0, 0.4);
  }
  100% {
    clip-path: circle(150% at 50% 50%);
    background: var(--theme-transition-bg-end);
    box-shadow: 0 0 50px 30px rgba(0, 0, 0, 0.6);
  }
}

.theme-container.theme-transition {
  position: relative;
  z-index: 1000;
  animation: themeTransition 0.5s forwards;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

.no-select {
  user-select: none;
}
