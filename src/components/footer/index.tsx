"use server";
import { ElementType } from "react";
import {
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaPinterest,
  FaYoutube,
} from "react-icons/fa";
import packageJson from "../../../package.json";
import { Logo } from "./logoFooter";

interface FooterProps {
  className?: string;
}

const LinkFooter = ({
  href,
  Icon,
  ariaLabel,
}: {
  href: string;
  Icon: ElementType;
  ariaLabel: string;
}) => {
  return (
    <a href={href} aria-label={ariaLabel}>
      <Icon className="text-xl text-textColorPrimary" />
    </a>
  );
};

const SocialNetworks = [
  {
    href: "https://www.youtube.com/pormadeonline",
    Icon: FaYoutube,
    ariaLabel: "YouTube",
  },
  {
    href: "https://br.linkedin.com/company/pormade-oficial",
    Icon: FaLinkedin,
    ariaLabel: "LinkedIn",
  },
  {
    href: "https://www.instagram.com/pormadeoficial/",
    Icon: FaInstagram,
    ariaLabel: "Instagram",
  },
  {
    href: "https://br.pinterest.com/pormadeoficial/",
    Icon: FaPinterest,
    ariaLabel: "Pinterest",
  },
  {
    href: "https://www.facebook.com/pormadeoficial/?locale=pt_BR",
    Icon: FaFacebook,
    ariaLabel: "Facebook",
  },
];

const Footer: React.FC<FooterProps> = ({ className }) => {
  return (
    <footer className={`bg-bgPrimaryWhite text-black text-center ${className}`}>
      <section className="flex flex-col md:flex-row items-center p-4 md:h-[180px]">
        <div className="w-full h-full flex-col sm:flex-row flex justify-center items-center">
          <div className="hidden sm:w-2/5 h-full justify-center flex-col items-center sm:flex ">
            <hr className="w-full h-[2px]  bg-PormadeGray" />
          </div>
          <div className="sm:w-1/5 h-full justify-center flex-col items-center flex rounded-md">
            <Logo />
            <div id="social" className="flex flex-row gap-3 mt-3">
              {SocialNetworks.map(({ href, Icon, ariaLabel }) => (
                <LinkFooter
                  href={href}
                  Icon={Icon}
                  ariaLabel={ariaLabel}
                  key={href}
                />
              ))}
            </div>
          </div>
          <div className="hidden sm:w-2/5 h-full justify-center flex-col items-center sm:flex ">
            <hr className="w-full h-[2px]  bg-PormadeGray" />
          </div>
        </div>
      </section>

      <section className="h-[30px] w-full  pt-[25px] pb-[25px] flex items-center justify-center border-t border-borderCopy">
        <p className="text-xs text-textCopy pr-1">v{packageJson.version}</p>
        <p className="text-xs text-textCopy">
          © {new Date().getFullYear()} Pormade. Todos os direitos reservados.
        </p>
      </section>
    </footer>
  );
};

export default Footer;
