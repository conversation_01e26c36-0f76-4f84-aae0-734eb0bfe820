"use client";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";

const ProductionSelect = () => {
  const { productionData } = useDataContext();
  const { currentProductionSelected, setCurrentProductionSelected } =
    useAcessContext();

  if (!productionData || productionData.length <= 1) return null;

  const currentIndex = productionData.findIndex(
    (production) => production.order === currentProductionSelected,
  );

  console.log("currentIndex", currentIndex);

  return (
    <div id="production-select-container" className="w-150" tabIndex={-1}>
      <Select
        onValueChange={(value: string) => {
          setCurrentProductionSelected(value);
        }}
        value={String(currentProductionSelected)}
        name="production-select"
        defaultValue={String(currentProductionSelected)}
      >
        <SelectTrigger>
          <SelectValue placeholder="Selecione a produção" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {productionData?.map((production, index) => (
              <SelectItem key={index} value={production?.order}>
                Produção {index + 1}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
};

export default ProductionSelect;
