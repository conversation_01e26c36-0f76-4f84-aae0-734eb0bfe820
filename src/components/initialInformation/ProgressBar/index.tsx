"use client";

import PopupWarning from "@/components/popupWarning";
import { useDataContext } from "@/contexts/data/dataContext";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";
import ProgressTimeline from "./progressTimeline";

import Tooltip from "@/components/tooltip";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import { CiCircleQuestion } from "react-icons/ci";

const ProgressBar = () => {
  const { proposalData, deliveriesData } = useDataContext();
  const { stage, getForecastDelivery } = useInitialInformation();
  const { getCurrentDelivery } = useActiveDelivery();

  return (
    <>
      {proposalData && typeof window !== "undefined" && stage ? (
        <div className={`w-full`}>
          {stage === "Aprovado" && proposalData?.hasProduction && (
            <PopupWarning stage={stage} nrProposal={proposalData?.proposal} />
          )}
          <div className={`py-5 tl480:py-8 px-0.5 sm:py-14 sm:px-5`}>
            <ProgressTimeline />
          </div>

          <div className="flex  justify-end items-center  gap-2">
            <h1 className="text-textColorPrimary text-end">
              {proposalData.hasDelivery && stage !== "Entregue" && (
                <>
                  {getForecastDelivery() ? (
                    <>
                      <strong>
                        {getCurrentDelivery({
                          delivery: deliveriesData,
                          isNew: proposalData?.newProposal,
                        })?.deliveryByPartner?.isPartner
                          ? "Entrega prevista para o parceiro: "
                          : "Entrega prevista para: "}
                      </strong>
                      {getForecastDelivery()}
                    </>
                  ) : (
                    ""
                  )}
                </>
              )}
            </h1>
            {getForecastDelivery() && stage !== "Entregue" && (
              <Tooltip text="A previsão está sujeita a alterações. Continue acompanhando as atualizações.">
                <CiCircleQuestion size={29} className="text-yellow-600" />
              </Tooltip>
            )}
          </div>
        </div>
      ) : (
        <ProgressBarLoading />
      )}
    </>
  );
};

const ProgressBarLoading = () => {
  return (
    <div className={`w-full`}>
      <div
        className={`py-8 px-0.5 sm:py-14 sm:px-5 justify-between items-between`}
      >
        <div className="h-3 bg-bgOrderNumberLoading rounded animate-pulseLoading mb-2"></div>
        <div className="h-3 bg-bgOrderNumberLoading rounded animate-pulseLoading mb-2"></div>
        <div className="h-3 bg-bgOrderNumberLoading rounded animate-pulseLoading mb-2"></div>
        <div className="h-3 bg-bgOrderNumberLoading rounded animate-pulseLoading mb-2"></div>
      </div>
    </div>
  );
};

export default ProgressBar;
