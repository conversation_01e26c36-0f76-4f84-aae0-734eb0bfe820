"use client";

import PopupWarning from "@/components/popupWarning";
import { useDataContext } from "@/contexts/data/dataContext";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";

const OrderNumber = () => {
  const { proposalData } = useDataContext();
  const { stage } = useInitialInformation();

  return (
    <section className="flex w-[150px]  justify-between">
      {proposalData ? (
        <div
          id="orderNumber"
          className="text-textColorPrimary w-full flex justify-between text-xs sm:text-base"
        >
          Proposta: <strong>{proposalData.proposal}</strong>
        </div>
      ) : (
        <OrderNumberLoading />
      )}
      {stage === "Aprovado" && proposalData && proposalData.hasProduction && (
        <PopupWarning stage={stage} nrProposal={proposalData?.proposal} />
      )}
    </section>
  );
};

const OrderNumberLoading = () => {
  return (
    <div className="flex bg-bgOrderNumberLoading justify-between h-6 w-24 animate-pulseLoading rounded-sm">
      <div></div>
    </div>
  );
};

export default OrderNumber;
