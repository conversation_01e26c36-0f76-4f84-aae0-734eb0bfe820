"use client";

import { useDataContext } from "@/contexts/data/dataContext";

const ClientName = () => {
  const { proposalData } = useDataContext();

  return (
    <section className="flex justify-between">
      {proposalData ? (
        <div id="client" className="text-textColorPrimary text-xs sm:text-base">
          Olá, <strong>{proposalData.customer}</strong>
        </div>
      ) : (
        <ClientNameLoading />
      )}
    </section>
  );
};

const ClientNameLoading = () => {
  return (
    <div className="flex bg-bgOrderNumberLoading justify-between h-6 w-24 animate-pulseLoading rounded-sm">
      <div></div>
    </div>
  );
};

export default ClientName;
