"use client";
import { useDataContext } from "@/contexts/data/dataContext";
import { formatCurrency } from "@/hooks/detailsProposal/functions/formatCurrency";

const FooterDetails = () => {
  const { proposalData } = useDataContext();

  if (!proposalData) {
    return <FooterDetailsLoading />;
  }

  const {
    totalValue: vlTotal,
    freightValue: vlFrete,
    carrier: transportadora,
  } = proposalData;

  return (
    <div className="mb-4 flex w-full sm:w-[80%] text-textColorPrimary">
      <div className="flex w-full flex-col gap-1 md:col-span-1">
        <FooterDetailItem
          label="Valor do Pedido:"
          value={formatCurrency(parseFloat(vlTotal || "0"))}
        />
        {transportadora.toLowerCase() === "retirar na fábrica" ? null : (
          <FooterDetailItem
            label="Frete:"
            value={
              vlFrete !== "0.000"
                ? formatCurrency(parseFloat(vlFrete || "0"))
                : "Grátis"
            }
          />
        )}

        {transportadora.toLowerCase() === "retirar na fábrica" ? (
          <FooterDetailItem
            label="Local de Retirada:"
            value="Retirar na Fábrica"
          />
        ) : (
          <FooterDetailItem label="Transportadora:" value={transportadora} />
        )}
      </div>
    </div>
  );
};

const FooterDetailItem = ({
  label,
  value,
}: {
  label: string;
  value: string;
}) => (
  <article className="flex w-full justify-between">
    <p className="text-sm font-medium md:text-sm lg:text-lg xl:text-lg">
      {label}
    </p>
    <p className="text-sm md:text-sm lg:text-lg xl:text-lg text-end">{value}</p>
  </article>
);

const FooterDetailsLoading = () => {
  return (
    <div className="mb-4 h-[110px]  flex w-full sm:w-[80%] text-textColorPrimary">
      <div className="flex w-full flex-col gap-1 md:col-span-1">
        <article className="flex w-full justify-between">
          <p className="text-sm h-[30px] animate-pulseLoading w-[20%]  font-medium md:text-sm lg:text-lg xl:text-lg"></p>
          <p className="text-sm  h-[30px] animate-pulseLoading w-[20%]  md:text-sm lg:text-lg xl:text-lg text-end"></p>
        </article>

        <article className="flex w-full justify-between">
          <p className="text-sm  h-[30px] animate-pulseLoading w-[20%]  font-medium md:text-sm lg:text-lg xl:text-lg"></p>
          <p className="text-sm   h-[30px] animate-pulseLoading w-[20%] md:text-sm lg:text-lg xl:text-lg text-end"></p>
        </article>
        <article className="flex  w-full justify-between">
          <p className="text-sm  h-[30px] animate-pulseLoading w-[20%]  font-medium md:text-sm lg:text-lg xl:text-lg"></p>
          <p className="text-sm  h-[30px] animate-pulseLoading w-[20%]  md:text-sm lg:text-lg xl:text-lg text-end"></p>
        </article>
      </div>
    </div>
  );
};

export default FooterDetails;
