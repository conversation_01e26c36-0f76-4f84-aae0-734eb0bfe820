export const ContentDetailsTableLoading = () => {
    return (
        <div className='h-[180px] flex items-center text-black'>
            <div className='flex h-full w-full'>
                <section className='w-[20%] overflow-hidden'>
                    <div className='w-full h-full animate-pulseLoading'></div>
                </section>
                <section className='w-[80%] h-full overflow-hidden mr-3'>
                    <div className='flex flex-col h-full justify-center items-center'>
                        <div className='flex flex-col w-full h-full justify-center items-center'>
                            <div className='flex w-[95%]  justify-between pb-2  items-end'>
                                <p className='font-bold text-base w-[20%]  h-4 animate-pulseLoading xl:text-2xl '></p>

                                <div className='flex  h-[85%] animate-pulseLoading w-[18%] justify-between'>
                                    <p className='text-base lg: xl:text-2xl'></p>
                                </div>
                            </div>
                            <div className='flex flex-col h-[85%] rounded-sm p-2 shadow-xl animate-pulseLoading  w-[95%]'>
                                <ul></ul>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    );
};
