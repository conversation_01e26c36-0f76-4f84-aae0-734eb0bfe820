"use client";
import { useDataContext } from "@/contexts/data/dataContext";
import { formatProductType } from "@/hooks/detailsProposal/functions/formatProductType";
import { Mestres } from "@/hooks/detailsProposal/productVetors/Mestres";
import { useDetailsContent } from "@/hooks/detailsProposal/useDetailsContent";
import { useOrderDetailsComponent } from "@/hooks/detailsProposal/useOrderDetailsComponent";
import React, { useEffect, useState } from "react";
import { IoIosInformationCircleOutline } from "react-icons/io";
import "./details.css";
import { ContentDetailsTableLoading } from "./loading";

const ImageContainer: React.FC<{
  type: string;
  components: {
    description: string;
    amount: number;
  }[];
  description?: string;
}> = ({ type, components, description }) => {
  const { fetchComponent } = useDetailsContent();
  const [isMestres, setIsMestres] = useState<boolean>(false);

  useEffect(() => {
    const containsMestre = Object.values(components).some((component) =>
      component.description.toLowerCase().includes("mestre"),
    );
    if (containsMestre) {
      setIsMestres(true);
    }
  }, [components]);

  return (
    <section className="h-full border bg-white color-black border-borderItemDetailsProposal  flex justify-center  rounded-md relative overflow-hidden  tl380:mr-3 tl380:mb-0">
      {isMestres ? (
        <>
          <Mestres />
        </>
      ) : (
        <> {fetchComponent(type, description)}</>
      )}
    </section>
  );
};

const ContentDetailsTable = () => {
  const {
    filteredComponents,
    totalQuantityValue,
    hasScroll,
    scrollContainerRef,
    componentsToRender,
    formatDescription,
  } = useOrderDetailsComponent();
  const { proposalData } = useDataContext();

  return (
    <>
      {proposalData && filteredComponents?.length > 0 ? (
        <>
          {filteredComponents.map((component) => (
            <main
              key={component.productType}
              className={`relative sm:h-[180px] w-full flex items-center text-textColorPrimary `}
            >
              <div className={`flex flex-col sm:flex-row h-full w-full`}>
                <section
                  id="cardDetailsImage"
                  className={`sm:w-[35%] lg:w-[25%] xl:w-[20%] tl380:w-[50%]  w-full h-[158px] sm:h-auto overflow-hidden`}
                >
                  <ImageContainer
                    type={component.productType}
                    components={componentsToRender}
                    description={component.description}
                  />

                  <div className="text-textObsImage  w-full sm:bottom-[-30px] absolute flex items-center justify-center tl380:justify-start">
                    <IoIosInformationCircleOutline />
                    <p className="ml-1 text-xs sm:text-base">
                      imagens meramente ilustrativas.
                    </p>
                  </div>
                </section>

                <section
                  id="cardDetails"
                  className={`sm:w-[65%] lg:w-[75%] xl:w-full w-[100%] h-full overflow-hidden mr-3 sm:mr-0`}
                >
                  <div
                    className={`flex flex-col h-full justify-center items-center`}
                  >
                    <div
                      className={`w-full h-full justify-center items-center`}
                    >
                      <div
                        className={`flex sm:w-full mb-4 sm:mb-0 mt-4 tl380:mt-0 tl380:absolute sm:relative top-0 right-0  rounded-sm   border border-borderDetailsProposal tl380:h-[158px] sm:h-auto justify-center items-center  sm:border-none  tl380:w-[50%] sm:top-0 flex-col sm:flex-row  sm:justify-between`}
                      >
                        <div
                          className={`flex w-full  sm:w-[50%] justify-center sm:justify-between`}
                        >
                          <p
                            className={`font-bold text-center text-base  xl:text-2xl`}
                          >
                            {formatProductType(component.productType)}
                          </p>
                        </div>
                        {totalQuantityValue !== 0 && totalQuantityValue ? (
                          <div
                            className={`flex w-full   sm:w-[50%] justify-center sm:justify-end`}
                          >
                            <p
                              className={`text-[12px]  pr-2 font-medium md:text-base  text-textColorPrimary`}
                            >
                              Quantidade:
                            </p>
                            <p
                              className={`text-[12px] font-medium md:text-base  text-textColorPrimary`}
                            >
                              {totalQuantityValue}
                            </p>
                          </div>
                        ) : (
                          <div className="flex w-full  sm:w-[50%] justify-center sm:justify-end">
                            <p
                              className={`text-[12px] w-32 h-4 animate-pulseLoading pr-2 font-medium md:text-base  text-textColorPrimary`}
                            ></p>
                            <p
                              className={`text-[12px] font-medium md:text-base  text-textColorPrimary`}
                            ></p>
                          </div>
                        )}
                      </div>

                      <div
                        className={`flex flex-col sm:h-[86%] lg:h-[85%] xl:h-[80%] w-full  mt-4 sm:mt-0 rounded-sm p-2  border border-borderDetailsProposal  overflow-hidden relative`}
                      >
                        <div
                          ref={scrollContainerRef}
                          className={`flex h-[95%] overflow-auto w-full ${hasScroll ? "icon" : ""}`}
                        >
                          <ul className="w-full">
                            {componentsToRender.map((component, index) => (
                              <li
                                key={`component-${component.description}-${index}`}
                                className="mb-0 p-1 text-[11px] sm:text-[10px]  border-b-[0.25px] last:border-none  sm:border-none border-borderDetailsProposal  md:text-xs whitespace-normal md:mb-1 break-words flex items-center"
                              >
                                <div className="flex  w-[25%] mr-2 h-full sm:w-[10%] xl:w-[8%] 2xl:w-[5%] justify-center items-center">
                                  <span className="flex items-center hover:transition-all w-[50px] rounded-full justify-center text-center bg-green-700 text-white text-[11px] font-bold ">
                                    {component.amount} un.
                                  </span>
                                </div>

                                <p className="w-[75%] sm:w-full ">
                                  {" "}
                                  {`${formatDescription(component.description)}`}
                                </p>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>
            </main>
          ))}
        </>
      ) : (
        <ContentDetailsTableLoading />
      )}
    </>
  );
};

export default ContentDetailsTable;
