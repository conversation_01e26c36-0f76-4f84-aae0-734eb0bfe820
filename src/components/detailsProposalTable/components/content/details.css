::-webkit-scrollbar {
    width: 2px; 
    height: 6px;
    border-radius: 30px;
    background-color: rgba(0, 0, 0, 0.08);
  }
  


::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.137);
}

[data-theme='dark'] ::-webkit-scrollbar {
    width: 2px; 
    height: 6px;
    border-radius: 30px;
    cursor: pointer;
    background-color: rgba(215, 215, 215, 0.04);
  }
  



[data-theme='dark'] ::-webkit-scrollbar-thumb {
    cursor: pointer;
  background-color: rgba(215, 215, 215, 0.5);
  }

@media screen and (max-width: 668px) {
  ::-webkit-scrollbar {
    width: 2px; 
    height: 3px;
    border-radius: 30px;
    background-color: rgba(0, 0, 0, 0.08);
  }
  


::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.137);
}

[data-theme='dark'] ::-webkit-scrollbar {
    width: 2px; 
    height: 3px;
    border-radius: 30px;
    cursor: pointer;
    background-color: rgba(215, 215, 215, 0.04);
  }
  



[data-theme='dark'] ::-webkit-scrollbar-thumb {
    cursor: pointer;
  background-color: rgba(215, 215, 215, 0.04);
  }
}


  .icon::before {
    content: "↓";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    animation: jump 1s infinite;
}


@keyframes jump {
  0% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-5px);
  }
  100% {
    transform: translateX(-50%) translateY(0);
  }
}

