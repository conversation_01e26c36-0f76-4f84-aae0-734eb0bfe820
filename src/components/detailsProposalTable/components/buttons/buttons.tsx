"use client";
import { useDataContext } from "@/contexts/data/dataContext";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import { useOrderDetailsComponent } from "@/hooks/detailsProposal/useOrderDetailsComponent";

const Buttons = () => {
  const {
    groupedComponents,
    currentProductType,
    setCurrentProductType,
    currentPageByType,
    setCurrentPageByType,
  } = useOrderDetailsComponent();
  const { proposalData } = useDataContext();
  const { setChangeProductTypeButton } = useDetailsProductContext();

  return (
    <>
      {proposalData &&
      groupedComponents &&
      Object.keys(groupedComponents).length ? (
        <>
          {Object.keys(groupedComponents).map((uniqueType: string, index) => (
            <button
              key={uniqueType + index}
              onClick={() => {
                setCurrentProductType(uniqueType);
                setChangeProductTypeButton(uniqueType);
                setCurrentPageByType({
                  ...currentPageByType,
                  [uniqueType]: 1,
                });
              }}
              className={`
              flex flex-row h-[85%] text-nowrap border-1 border-gray-400 bg-bgButtonDetails px-3 justify-center items-center  transition duration-600 sm:hover:p-3 transform sm:hover:-translate-y-0.5 motion-reduce:transition-none motion-reduce:transform-none
              ${currentProductType === uniqueType ? "text-textButtonDetailsProposal  bg-textButtonBgActive" : ""}
              `}
            >
              {uniqueType}
            </button>
          ))}
        </>
      ) : (
        <ButtonsLoading />
      )}
    </>
  );
};

const ButtonsLoading = () => {
  return (
    <>
      {[...Array(3)].map((_, index) => (
        <button
          key={index + 1}
          aria-label="Loading"
          className="flex animate-pulseLoading flex-row font-semibold  mr-[0.7px] px-1 py-1 text-sm md:px-1 md:text-lg md:py-2 lg:px-[0.45rem] lg:text-[14px] lg:py-1 xl:text-lg bg-gray-300"
        >
          <div className=" h-6 w-28"></div>
        </button>
      ))}
    </>
  );
};

export default Buttons;
