"use server";
import Buttons from "./components/buttons/buttons";
import ContentDetailsTable from "./components/content";
import FooterDetails from "./components/footerDetails";

export const DetailsProposalTable = async () => {
  return (
    <main
      className={` w-full h-full  rounded-xl`}
      style={{
        position: "relative",
        boxShadow: "0px 0px 5px var(--shadow-color-card)",
      }}
    >
      <section
        className={`bg-PormadeGreen text-textColorTitleCard shadow p-4 rounded-t-xl`}
        style={{
          textShadow: "1px 1px 1px var(--shadow-text-title-card)",
        }}
      >
        <h1 className={`tl380:text-2xl font-bold text-left textShadow`}>
          Detalhes do Pedido
        </h1>
      </section>
      <section
        className={`flex bg-bgPrimaryWhite h-[50px] sm:h-[40px] pl-1 items-center gap-1  text-textColorPrimary overflow-auto flex-row  justify-start border-b border-borderItemDetailsProposal`}
      >
        <Buttons />
      </section>
      <section
        className={`flex-1  rounded-b-xl sm:h-[450px] py-6 px-3 bg-bgPrimaryWhite sm:py-4 md:py-5`}
      >
        <ContentDetailsTable />
        <hr
          className={`border-t mt-4 sm:mt-16 mb-7 md:mb-6 border-borderItemDetailsProposal  md:mt-9 tex-black`}
        />
        <h2
          className={`text-lg  md:text-xl lg:text-2xl font-bold mb-2 md:mb-4 lg:mb-4 mt-8 text-textColorPrimary`}
        >
          Resumo
        </h2>
        <FooterDetails />
      </section>
    </main>
  );
};
