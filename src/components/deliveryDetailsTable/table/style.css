.event-icon {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.event-icon::before {
  content: '';
  position: absolute;
  width: 2px; 
  left: 50%; 
  /* top: calc(50% + 8px); */
  top: 50%;
  transform: translateX(-50%);
  height: calc(100% + 50px); 
  background-color: var(--bg-progress-timeline);
  z-index: -1;
}



.event-last .event-icon::before {
  /* display: none; */
  background-color: var(--bg-primary-white);
  width: 4px;
  height: calc(100%);
}

.crossmark {
  width: 96px;
  height: 96px;
}

.crossmark-circle {
  fill: none;
  stroke-dasharray: 88;
  stroke-dashoffset: 88;
  animation: draw-circle 1s ease-in-out forwards;
}

.crossmark-cross {
  fill: none;
  stroke-dasharray: 24;
  stroke-dashoffset: 24;
  animation: draw-cross 1s ease-in-out forwards 1s;
}

@keyframes draw-circle {
  to {
    stroke-dashoffset: 0;
  }
}

@keyframes draw-cross {
  to {
    stroke-dashoffset: 0;
  }
}


@keyframes drop-in {
  0% {
    transform: translateY(-50%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.event-item {
  opacity: 0;
  transform: translateY(-50%);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.event-item.loaded {
  opacity: 1;
  transform: translateY(0);
  animation: drop-in 0.5s forwards;
}
