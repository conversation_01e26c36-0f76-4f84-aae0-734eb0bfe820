import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import useDeliveriesDetails from "@/hooks/deliveriesDetails/useDeliveriesDetails";
import { useEffect } from "react";
import "./style.css";

import { useDataContext } from "@/contexts/data/dataContext";
import { formatDescription } from "@/hooks/deliveriesDetails/functions/formatDescription";
import { NoEventsYet, NotDelivered } from "./not-events";

import { useAcessContext } from "@/contexts/acess/acessContext";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import { useDeliveryItens } from "@/hooks/deliveriesDetails/useDeliveryItens";
import { useCurrentProduction } from "@/hooks/productionProgress/useCurrentProduction";
import { formatDateAndTime } from "@/lib/formatDate";

const EventItem = () => {
  const {
    regexDate,
    regexDateAndTime,
    visibleIndices,
    events,
    animationKeyDelivery,
  } = useDeliveryItens();

  console.log("events", events);

  return (
    <>
      {events.map((event, index) => (
        <li
          key={`${event.description}-${index}-${animationKeyDelivery}`}
          className={`w-full mb-6 ${
            events.length === 1 || index === events.length - 1
              ? "event-item event-last"
              : "event-item"
          } flex ${visibleIndices.includes(index) ? "loaded" : ""} h-auto relative`}
        >
          <EventDate
            date={event.eventDate}
            regexDate={regexDate}
            regexDateAndTime={regexDateAndTime}
          />
          <EventIcon />
          <EventDescription description={event.description} />
        </li>
      ))}
    </>
  );
};

interface EventDateProps {
  date: string;
  regexDate: RegExp;
  regexDateAndTime: RegExp;
}

const EventDate = ({ date, regexDate, regexDateAndTime }: EventDateProps) => (
  <div className="event-date w-[25%] tl380:w-[15%] sm:w-[20%] text-xs sm:text-sm text-textColorPrimary font-semibold flex justify-center items-center text-center">
    {regexDate.test(date) ? (
      <>{date}</>
    ) : (
      <>{regexDateAndTime.test(date) ? date : formatDateAndTime(date)}</>
    )}
  </div>
);

const EventIcon = () => (
  <div className="event-icon w-[25%] tl380:w-[20%] flex items-center relative">
    <div className="w-[15px] h-[15px] rounded-full bg-PormadeGreen"></div>
  </div>
);

const EventDescription = ({ description }: { description: string }) => {
  const { deliveriesData, productionData, proposalData } = useDataContext();
  const { currentProductionSelected } = useAcessContext();
  const { getCurrentDelivery } = useActiveDelivery();
  const { getCurrentProduction } = useCurrentProduction();

  // const productionSelected = useMemo(() => {
  //   if (proposalData?.newProposal) {
  //     return productionData?.find(
  //       (item) => item.order === currentProductionSelected,
  //     );
  //   }

  //   return productionData?.[0];
  // }, [currentProductionSelected, productionData, proposalData?.newProposal]);

  const delivery = getCurrentDelivery({
    delivery: deliveriesData,
    isNew: proposalData?.newProposal,
  });

  if (
    getCurrentProduction({
      production: productionData,
      isNew: proposalData?.newProposal,
    })?.awaitingCollectionDate
  ) {
    return (
      <div className="event-description text-start w-[50%] tl380:w-[65%] text-xs sm:text-sm flex text-textColorPrimary font-semibold justify-start items-center">
        {formatDescription(
          description,
          delivery?.deliveryByPartner?.isDoubleDeliverySsw,
          delivery?.deliveryByPartner?.isDoubleDeliveryFretefy,
          delivery?.deliveryByPartner?.isPartner,
        )}
      </div>
    );
  }

  if (deliveriesData?.length === 1 && !deliveriesData[0].order) {
    return (
      <div className="event-description text-start w-[50%] tl380:w-[65%] text-xs sm:text-sm flex text-textColorPrimary font-semibold justify-start items-center">
        {formatDescription(
          description,
          deliveriesData?.[0]?.deliveryByPartner?.isDoubleDeliverySsw,
          deliveriesData?.[0]?.deliveryByPartner?.isDoubleDeliveryFretefy,
          deliveriesData?.[0]?.deliveryByPartner?.isPartner,
        )}
      </div>
    );
  }
};
export const EventsRow = () => {
  const { events } = useDeliveriesDetails();
  const { scrollEventsRef } = useDetailsProductContext();
  const { productionData, proposalData } = useDataContext();
  const { currentProductionSelected } = useAcessContext();
  const { animationKeyDelivery } = useDetailsProductContext();
  const { getCurrentProduction } = useCurrentProduction();

  useEffect(() => {
    if (scrollEventsRef.current) {
      scrollEventsRef.current.scrollTop = 0;
    }
  }, [animationKeyDelivery, scrollEventsRef]);

  console.log("events", events);

  if (!proposalData?.hasDelivery) {
    return <NotDelivered />;
  } else if (
    events.length === 0 &&
    !getCurrentProduction({
      production: productionData,
      isNew: proposalData?.newProposal,
    })?.awaitingCollectionDate
  ) {
    return <NoEventsYet />;
  }

  // else if (events.length === 0 && !productionData?.dataAguardandoColeta) {
  //   return <NoEventsYet />;
  // }

  return (
    <main
      className="w-full  overflow-auto p-[15px] sm:p-4 h-full bg-bgPrimaryWhite  rounded-sm"
      ref={scrollEventsRef}
    >
      <div className="w-full h-full">
        <ul className="w-full pb-2 relative">
          <EventItem />
        </ul>
      </div>
    </main>
  );
};
