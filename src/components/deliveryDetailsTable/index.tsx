"use client";

import { useState } from "react";
import { EventsRow } from "./table";
import { useDataContext } from "@/contexts/data/dataContext";
import { EventsLoading } from "./table/eventsLoading";

const DeliveryDetailsTable = () => {
  const [isGrabbing, setIsGrabbing] = useState(false);
  const { proposalData } = useDataContext();

  return (
    <button
      className={`w-full no-select  flex flex-col relative ${isGrabbing ? "cursor-grabbing" : "cursor-grab"} rounded-xl shadow h-full`}
      style={{
        position: "relative",
      }}
      tabIndex={-1}
      onMouseDown={() => setIsGrabbing(true)}
      onMouseUp={() => setIsGrabbing(false)}
      onMouseLeave={() => setIsGrabbing(false)}
    >
      <header
        className="bg-PormadeGreen w-full text-textColorTitleCard shadow p-4 rounded-t-xl"
        style={{ textShadow: "1px 1px 1px var(--shadow-text-title-card)" }}
      >
        <h1 className="tl380:text-2xl font-bold text-center sm:text-start">
          Entrega
        </h1>
      </header>
      {proposalData ? (
        <>
          {" "}
          <div className="rounded-b-xl w-full bg-bgPrimaryWhite ">
            <div className="flex p-5 w-full overflow-hidden h-full flex-col  max-h-[450px] min-h-[450px]">
              <div className="flex  w-full h-[50px] sm:h-[40px] justify-center items-center">
                <div className="w-[40%] flex justify-start font-bold">
                  <h1 className="text-xs sm:text-base text-textColorPrimary">
                    Informações da Entrega
                  </h1>
                </div>
                <hr className="w-[60%] h-[1px] border-borderDetailsProposal" />
              </div>

              <div
                className="m-3 pb-6
           w-full h-full 0"
              >
                <EventsRow />
              </div>
            </div>
            <div className="text-xs min-h-[40px] items-center text-center text-textColorPrimary border-t  border-borderItemTableDelivery  bottom-0 absolute flex justify-center w-full">
              <p>
                OBS: As informações de entrega são de total responsabilidade da
                transportadora.
              </p>
            </div>
          </div>
        </>
      ) : (
        <EventsLoading />
      )}
    </button>
  );
};

export default DeliveryDetailsTable;
