"use client";

import { useDashboardAdminFilterContext } from "@/contexts/admin/dashboard-admin-filter";
import { useDashboardItems } from "@/hooks/admin/dashboard/dashboard.items.hook";
import { useTrackingFindAll } from "@/hooks/admin/dashboard/tracking-find-all.hook";
import { Accordion } from "@radix-ui/react-accordion";
import { AnimatePresence, motion } from "framer-motion";
import { FileX } from "lucide-react";
import { AccordionCards } from "./acordion-cards";
import { TrackingFilters } from "./filters";
import { TrackingModal } from "./modal";
import { StatusCard } from "./status-card";

export const TrackingDashboard: React.FC = () => {
  const { proposalNumber, status, date, type, itemsLimitPerPage } =
    useDashboardAdminFilterContext();

  const finished =
    type === "finalizado" ? true : type === "em andamento" ? false : undefined;

  const { data, isLoading } = useTrackingFindAll({
    limit: itemsLimitPerPage,
    proposal: proposalNumber,
    statusId: status,
    finished,
    lastUpdateDate: date,
  });

  const {
    groupedData,
    statusesToRender,
    selectedTracking,
    isModalOpen,
    openModal,
    closeModal,
  } = useDashboardItems({ data, isLoading });

  return (
    <main className="flex h-full  flex-col flex-1 bg-bgPrimaryWhite rounded-lg">
      <header className="bg-PormadeGray md:px-0 px-4 py-2 border-b border-PormadeGreen">
        <h1 className="text-2xl md:text-4xl font-extrabold text-center text-textColorPrimary mb-4">
          Dashboard de Pedidos
        </h1>
        <TrackingFilters />
      </header>
      <section className="flex-1 min-h-0 flex w-full justify-center items-start py-4 bg-PormadeGray">
        {!isLoading && statusesToRender.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-10">
            <FileX className="w-12 h-12 text-textColorPrimary mb-4" />
            <span className="text-center text-textColorPrimary text-lg">
              Nenhuma proposta encontrada.
            </span>
          </div>
        ) : (
          <>
            <div className="hidden    md:flex flex-wrap justify-start items-stretch gap-6">
              {isLoading && (
                <>
                  {[...Array(6)].map((_, i) => (
                    <div
                      key={i}
                      className="w-[250px] rounded-lg h-[300px] animate-pulseLoading"
                    ></div>
                  ))}
                </>
              )}
              {!isLoading && (
                <AnimatePresence mode="wait">
                  {statusesToRender.map(({ label }) => (
                    <StatusCard
                      key={label}
                      status={label}
                      proposals={groupedData[label] || []}
                      onSelectProposal={openModal}
                      isLoading={isLoading}
                    />
                  ))}
                </AnimatePresence>
              )}
            </div>
            <div className="flex  w-full md:hidden flex-col gap-4 px-4">
              {!isLoading && (
                <Accordion
                  type="multiple"
                  className="w-full text-textColorPrimary gap-3 flex flex-col"
                >
                  {statusesToRender.map(({ label }) => (
                    <AccordionCards
                      key={label}
                      status={label}
                      proposals={groupedData[label] || []}
                      onSelectProposal={openModal}
                      isLoading={isLoading}
                    />
                  ))}
                </Accordion>
              )}
            </div>
          </>
        )}

        <TrackingModal
          isOpen={isModalOpen}
          tracking={selectedTracking || undefined}
          onClose={closeModal}
          idProposal={selectedTracking && selectedTracking.id}
        />
      </section>

      <footer className="bg-bgPrimaryWhite sticky  py-2 px-14 border-t border-PormadeGreen">
        <motion.h2
          className="text-sm md:text-xl text-textColorPrimary font-medium text-right"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          Total de propostas: {data?.success ? data.data.length : 0}
        </motion.h2>
      </footer>
    </main>
  );
};
