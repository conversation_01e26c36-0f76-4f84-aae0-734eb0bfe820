import { StatusStringType } from "@/services/admin/requests/tracking-find-all";
import {
  Check,
  CheckCircle,
  Navigation2,
  Package,
  Settings,
  Truck,
} from "lucide-react";
import React from "react";

export enum StatusEnum {
  "Aprovado" = 1,
  "Em produção" = 2,
  "Manufaturado" = 3,
  "Expedição" = 4,
  "Em trânsito" = 5,
  "Entregue" = 6,
}

export const STATUSES: {
  label: StatusStringType;
  icon: React.ComponentType;
}[] = [
  { label: "Aprovado", icon: CheckCircle },
  { label: "Em produção", icon: Settings },
  { label: "Manufaturado", icon: Package },
  { label: "Expedido", icon: Truck },
  { label: "Em trânsito", icon: Navigation2 },
  { label: "Entregue", icon: Check },
];

export const getColorForStatus = (
  status: StatusStringType | undefined,
): string => {
  const colors: Record<StatusStringType, string> = {
    Aprovado: "#28a745",
    "Em produção": "#ffc107",
    "Em trânsito": "#17a2b8",
    Expedido: "#007bff",
    Manufaturado: "#6f42c1",
    Entregue: "#6f42c1",
  };
  if (status === undefined) {
    return "#343a40";
  }
  return colors[status];
};
