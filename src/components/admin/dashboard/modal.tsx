import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { useGenerateNewTrackingLink } from "@/hooks/admin/dashboard/generate-tracking-link.hook";
import { ITrackingProposal } from "@/services/admin/requests/tracking-find-all";
import { Link } from "lucide-react";
import React from "react";
import { getColorForStatus, STATUSES } from "./constants";

interface TrackingModalProps {
  tracking: ITrackingProposal | undefined;
  isOpen: boolean;
  onClose: () => void;
  idProposal: number | null;
}

export const TrackingModal: React.FC<TrackingModalProps> = ({
  tracking,
  isOpen,
  onClose,
  idProposal,
}) => {
  const borderColor = getColorForStatus(tracking?.status || undefined);
  const statusConfig = STATUSES.find((s) => s.label === tracking?.status);
  const Icon =
    (statusConfig?.icon as React.ComponentType<{
      className?: string;
      style?: React.CSSProperties;
    }>) ?? (() => null);

  const { generateNewTrackingLink } = useGenerateNewTrackingLink();

  return (
    <Modal
      className="w-[500px]"
      isOpen={isOpen}
      shouldCloseOnOverlayClick
      onRequestClose={onClose}
    >
      <div
        style={{ borderColor }}
        className="relative flex justify-between border bg-bgPrimaryWhite bg-opacity-70 rounded-2xl shadow-2xl p-6 w-full z-10 transform transition-all duration-300 ease-in-out"
      >
        <div className="w-3/5 pr-4">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-textColorPrimary hover:text-gray-800 text-2xl focus:outline-none"
          >
            &times;
          </button>
          <h2 className="text-3xl font-bold text-textColorPrimary mb-6">
            Detalhes
          </h2>
          <div className="space-y-3 text-textColorPrimary">
            <p className="flex gap-2">
              <span className="font-semibold">Proposta:</span>
              <span className="text-textGrayInititalInformation">
                {tracking?.proposal}
              </span>
            </p>
            <p className="flex gap-2">
              <span className="font-semibold">Status:</span>{" "}
              <span className="text-textGrayInititalInformation">
                {tracking?.status}
              </span>
            </p>
            <p className="flex gap-2">
              <span className="font-semibold">Finalizado:</span>
              <span className="text-textGrayInititalInformation">
                {tracking?.finished ? "Sim" : "Não"}
              </span>
            </p>
            <p className="flex gap-2">
              <span className="font-semibold">Criado Em:</span>
              <span className="text-textGrayInititalInformation">
                {tracking?.createdAt}
              </span>
            </p>
            <p className="flex gap-2">
              <span className="font-semibold">Atualizado Em:</span>
              <span className="text-textGrayInititalInformation">
                {tracking?.updatedAt}
              </span>
            </p>
          </div>
        </div>
        <div className="flex flex-col justify-between items-center">
          <div className="flex justify-center items-center w-24 h-24 mb-4">
            <Icon style={{ color: borderColor }} className="w-20 h-20" />
          </div>
          {idProposal && (
            <Button
              onClick={() => generateNewTrackingLink({ id: idProposal })}
              style={{
                borderColor: borderColor,
                backgroundColor: `${borderColor}33`,
              }}
              className="border text-textColorPrimary font-semibold px-4 py-2 rounded-md hover:bg-opacity-80 transition-colors duration-200"
            >
              Gerar Link <Link className="ml-2" />
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};
