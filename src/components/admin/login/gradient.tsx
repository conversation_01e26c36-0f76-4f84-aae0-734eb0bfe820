"use client";
import React from "react";

interface GreenGradientTextureBackgroundProps {
  className?: string;
}

export const GreenGradientTextureBackground: React.FC<
  GreenGradientTextureBackgroundProps
> = ({ className = "" }) => {
  return (
    <div
      className={`absolute inset-0 opacity-90 rounded-br-[10rem] ${className}`}
    >
      <svg
        className="w-full h-full rounded-br-[10rem]"
        preserveAspectRatio="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="greenGradient" x1="0" y1="0" x2="1" y2="1">
            <stop offset="0%" stopColor="#079d3a" />
            <stop offset="100%" stopColor="var(--primary-color-green)" />
          </linearGradient>

          <pattern
            id="trianglePattern"
            patternUnits="userSpaceOnUse"
            width="120"
            height="120"
          >
            <polygon
              points="60,0 120,120 0,120"
              fill="rgba(255,255,255,0.05)"
            />
          </pattern>
        </defs>

        {/* Fundo com gradiente */}
        <rect width="100%" height="100%" fill="url(#greenGradient)" />
        <rect width="100%" height="100%" fill="url(#trianglePattern)" />
      </svg>
    </div>
  );
};
