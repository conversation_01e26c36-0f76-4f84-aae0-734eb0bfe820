import { useTheme } from "@/contexts/theme/themeContext";
import { PercentCircleProps } from "@/types/production";
import React, { useEffect, useRef, useState } from "react";
import "./index.module.css";

const PercentageCircleNewPropostal: React.FC<PercentCircleProps> = ({
  percentage,
  raio: radius = 25,
  strokeWidth = 8,
  clockwise = true,
  animationSpeed = 10,
  showLegend = false,
  fontSize = 0.8,
  currentProductType,
}) => {
  const { theme } = useTheme();
  const circumference = 2 * Math.PI * radius;
  const [animatedPercentage, setAnimatedPercentage] = useState(0);
  const circleRef = useRef<SVGCircleElement>(null);

  useEffect(() => {
    setAnimatedPercentage(0);
    if (circleRef.current) {
      circleRef.current.style.strokeDashoffset = `${circumference}px`;
    }

    let currentPercentage = 0;
    const animationInterval = setInterval(() => {
      setAnimatedPercentage(currentPercentage);

      const progress = (currentPercentage / 100) * circumference;
      const offset = clockwise ? circumference - progress : progress;
      if (circleRef.current) {
        circleRef.current.style.strokeDashoffset = `${offset}px`;
      }

      if (currentPercentage >= percentage) {
        clearInterval(animationInterval);
      } else {
        currentPercentage = Math.min(percentage, currentPercentage + 1);
      }
    }, animationSpeed);

    return () => {
      clearInterval(animationInterval);
    };
  }, [percentage, circumference, clockwise, animationSpeed]);

  const svgExtraHeight = showLegend ? 50 : 0;
  const svgSize = radius * 2 + strokeWidth * 2;
  const svgHeight = svgSize + svgExtraHeight;

  return (
    <div className="flex justify-center items-center">
      <svg
        width={svgSize}
        height={svgHeight}
        viewBox={`0 0 ${svgSize} ${svgHeight}`}
        style={{
          fill: "none",
          stroke: "none",
          strokeWidth: 0,
          display: "inline",
        }}
      >
        <circle
          cx={radius + strokeWidth}
          cy={radius + strokeWidth}
          r={radius}
          stroke={theme === "light" ? "#d3d7ce" : "#2d2c2c"}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        <circle
          ref={circleRef}
          cx={radius + strokeWidth}
          cy={radius + strokeWidth}
          r={radius}
          stroke={
            currentProductType?.toLowerCase() === "acessórios"
              ? "rgb(202 138 42)"
              : "#16a34a"
          }
          strokeWidth={strokeWidth}
          strokeDasharray={circumference}
          fill="transparent"
          strokeDashoffset={circumference}
          transform={`rotate(-90 ${radius + strokeWidth} ${radius + strokeWidth})`}
        />

        <text
          x={radius + strokeWidth}
          y={radius + strokeWidth}
          dominantBaseline="middle"
          textAnchor="middle"
          fill={theme === "light" ? "#000" : "#fff"}
          style={{ fontWeight: "bold", fontSize: `${fontSize}rem` }}
        >
          {animatedPercentage}%
        </text>
        {showLegend && (
          <text
            x={svgSize / 2}
            y={svgSize + svgExtraHeight / 2}
            textAnchor="middle"
            fill={theme === "light" ? "#000" : "#fff"}
            style={{ fontSize: "0.8rem" }}
          >
            Progresso
          </text>
        )}
      </svg>
    </div>
  );
};

export default PercentageCircleNewPropostal;
