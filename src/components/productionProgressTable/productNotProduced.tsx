"use client";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";

export const NotRegistered = () => {
  const { changeProductTypeButton } = useDetailsProductContext();
  const { animationKeyProduction } = useDetailsProductContext();

  return (
    <div
      key={`not-registered-${(changeProductTypeButton as string) + animationKeyProduction}`}
      className="h-full w-full flex-col flex justify-center items-center text-textColorPrimary"
    >
      <div className="flex flex-col gap-3 h-full w-full justify-center items-center">
        <svg
          className="checkmark"
          width="96"
          height="96"
          viewBox="0 0 32 32"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            className="checkmark-circle"
            cx="16"
            cy="16"
            r="14"
            stroke="var(--primary-color-green)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            className="checkmark-check"
            d="M8 17l5 5L24 10"
            stroke="var(--primary-color-green)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        <p className="text-2xl text-textColorPrimary text-center textShadow">
          Produto separado
        </p>
      </div>
    </div>
  );
};

export const NotStartedProduction = () => {
  const { changeProductTypeButton, animationKeyProduction } =
    useDetailsProductContext();

  return (
    <div
      key={`not-started-${(changeProductTypeButton as string) + animationKeyProduction}`}
      className="h-full w-full flex flex-col justify-center items-center text-textColorPrimary"
    >
      <div className="flex flex-col gap-3 justify-center items-center">
        <svg
          className="not-started-icon text-PormadeGreen"
          width="96"
          height="96"
          viewBox="0 0 32 32"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 4h16l-8 12 8 12H8l8-12-8-12z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
        <p className="text-2xl text-center">Produção não iniciada</p>
      </div>
    </div>
  );
};
