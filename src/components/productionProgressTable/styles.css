  
  .progressBar {
    position: absolute;
    background: linear-gradient(-45deg, #079D3A, #8BC34A, #079D3A, #079D3A);
    background-size: 400% 400%;
 
    top: 0;
    left: 0;
    height: 100%;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    overflow: hidden;
  }

  .border-radius {
    border-top-right-radius: 120px;
    animation: radiusWave 2s ease-in-out infinite;
  }
  
  .progressBar::before{
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
    url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='1600' height='198'%3e%3cdefs%3e%3clinearGradient id='a' x1='50%25' x2='50%25' y1='-10.959%25' y2='100%25'%3e%3cstop stop-color='%23fcc900' stop-opacity='.25' offset='0%25'/%3e%3cstop stop-color='%23fcc900' offset='100%25'/%3e%3c/linearGradient%3e%3c/defs%3e%3cpath fill='url(%23a)' fill-rule='evenodd' d='M.005 121C311 121 409.898-.25 811 0c400 0 500 121 789 121v77H0s.005-48 .005-77z'/%3e%3c/svg%3e"),
    url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='1600' height='198'%3e%3cdefs%3e%3clinearGradient id='a' x1='50%25' x2='50%25' y1='-10.959%25' y2='100%25'%3e%3cstop stop-color='%23fcc900' stop-opacity='.25' offset='0%25'/%3e%3cstop stop-color='%23fcc900' offset='100%25'/%3e%3c/linearGradient%3e%3c/defs%3e%3cpath fill='url(%23a)' fill-rule='evenodd' d='M.005 121C311 121 409.898-.25 811 0c400 0 500 121 789 121v77H0s.005-48 .005-77z'/%3e%3c/svg%3e"),
    url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='1600' height='198'%3e%3cdefs%3e%3clinearGradient id='a' x1='50%25' x2='50%25' y1='-10.959%25' y2='100%25'%3e%3cstop stop-color='%23fcc900' stop-opacity='.25' offset='0%25'/%3e%3cstop stop-color='%23fcc900' offset='100%25'/%3e%3c/linearGradient%3e%3c/defs%3e%3cpath fill='url(%23a)' fill-rule='evenodd' d='M.005 121C311 121 409.898-.25 811 0c400 0 500 121 789 121v77H0s.005-48 .005-77z'/%3e%3c/svg%3e");
    background-repeat: repeat-x;
    animation: 20s waves linear infinite forwards;
    background-size: 1600px 80%;
    background-position: 0 130%, -50px 130%, 500px 130%;
    opacity: 0.3;
    
  }
  
  @keyframes waves {
    to {
      background-position: 1600px 130%, 3150px 130%, 5300px 130%;
    }
  }


  @keyframes waterFlow {
    0% { background-position: 0 50%; transform: scale(1); }
    100% { background-position: 100% 50%; transform: perspective(100px); }
  }
  
  @keyframes waveMotion {
    0%, 100% { background-position: 100% 0; }
    50% { background-position: 0 0; }
  }
  
  @keyframes radiusWave {
    0%, 100% {
      border-top-right-radius: 120px;
    }
    50% {
      border-top-right-radius: 100px;
    }
  }

  .checkmark {
    width: 96px;
    height: 96px;
  }
  
  .checkmark-circle {
    fill: none;
    stroke-dasharray: 88;
    stroke-dashoffset: 88;
    animation: draw-circle 1s ease-in-out forwards;
  }
  
  .checkmark-check {
    fill: none;
    stroke-dasharray: 24;
    stroke-dashoffset: 24;
    animation: draw-check 1s ease-in-out forwards 1s;
  }
  
  @keyframes draw-circle {
    to {
      stroke-dashoffset: 0;
    }
  }
  
  @keyframes draw-check {
    to {
      stroke-dashoffset: 0;
    }
  }
  