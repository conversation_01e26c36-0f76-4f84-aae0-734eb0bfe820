"use client";
import { useOrderDetailsComponent } from "@/hooks/detailsProposal/useOrderDetailsComponent";

import { useProductionProgressNewProposal } from "@/hooks/productionProgress/useProductionProgress";

import { useDataContext } from "@/contexts/data/dataContext";
import { useState } from "react";
import "./styles.css";

import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import { formatProductType } from "@/hooks/detailsProposal/functions/formatProductType";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";
import { useProductionProgressOld } from "@/hooks/productionProgress/oldProductionProgress";
import { useCurrentProduction } from "@/hooks/productionProgress/useCurrentProduction";
import { MdHourglassEmpty } from "react-icons/md";
import PercentageCircleNewPropostal from "./percentAnimation";
import OldPercentage from "./percentAnimation/oldAnimation";
import { NotRegistered, NotStartedProduction } from "./productNotProduced";

const CardProductionProgress = () => {
  const { currentProductType } = useOrderDetailsComponent();
  const { getItems } = useProductionProgressNewProposal();
  const { stage } = useInitialInformation();
  const { productionData, proposalData } = useDataContext();
  const { isNewProposal } = useAcessContext();
  const { currentPercent } = useProductionProgressOld();
  const { currentProductionSelected } = useAcessContext();
  const { animationKeyProduction } = useDetailsProductContext();
  const { getCurrentProduction } = useCurrentProduction();

  return (
    <>
      <div className="flex bg-bgPrimaryWhite h-[50px]   pl-1 items-center  gap-1 text-textColorPrimary overflow-auto flex-row  justify-center ">
        {isNewProposal && productionData && productionData?.length > 1 ? (
          <div className="flex w-full items-center justify-between gap-2 flex-row ">
            <h1 className="text-md ml-4 sm:text-lg">Progresso de:</h1>
            <div className="flex mr-5  items-center gap-2 flex-row ">
              <div className="flex items-center gap-2 rounded-md bg-green-600/20 px-3 py-1">
                <span className="w-3 h-3 rounded-lg flex bg-green-600"></span>
                <h1 className="text-green-600">Produção</h1>
              </div>
              <div className="flex items-center gap-2 rounded-md bg-yellow-600/20 px-3 py-1">
                <span className="w-3 h-3 rounded-lg flex bg-yellow-600"></span>
                <h1 className="text-yellow-600">Separação</h1>
              </div>
            </div>
          </div>
        ) : (
          <>
            {currentPercent !== null ? (
              <p className="absolute mt-10 text-md sm:text-lg  text-textColorPrimary">
                Progresso de{" "}
                {currentProductType?.toLowerCase() !== "acessórios"
                  ? "produção "
                  : "separação "}
                de
                <strong> {formatProductType(currentProductType)}</strong>
              </p>
            ) : (
              <p className="absolute mt-10 text-md sm:text-lg  text-textColorPrimary">
                <strong>{formatProductType(currentProductType)}</strong>
              </p>
            )}
          </>
        )}
      </div>
      {stage === "Aprovado" ||
      !getCurrentProduction({
        production: productionData,
        isNew: proposalData?.newProposal,
      })?.startProductionDate ? (
        <div className="rounded-b-xl h-[450px] py-6 px-3 bg-bgPrimaryWhite  sm:py-4 md:py-5">
          <div className="w-full flex flex-col h-full justify-center items-center">
            <MdHourglassEmpty className="text-6xl mb-5 text-textColorPrimary mr-2" />
            <h1 className="text-xl font-bold text-textColorPrimary">
              {!proposalData?.hasProduction
                ? "Esse pedido não possui rastreamento de produção"
                : "A produção ainda não iniciou"}
            </h1>
          </div>
        </div>
      ) : (
        <div className="rounded-b-xl h-[450px]  overflow-y-auto py-6 px-3 bg-bgPrimaryWhite  sm:py-4 md:py-5">
          <div className="w-full flex  flex-row h-full justify-center items-center">
            <div
              key={animationKeyProduction + "1"}
              className="w-full h-full max-h-full"
            >
              {isNewProposal && (productionData?.length ?? 0) > 1 ? (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    {getItems?.map((item, index) => (
                      <div
                        key={index}
                        style={{
                          border: `1px solid ${
                            item.productType.toLowerCase() === "acessórios"
                              ? "#ca8a04"
                              : "#16a34a"
                          }`,
                        }}
                        className={`rounded-lg flex flex-col justify-center items-center p-4`}
                      >
                        {item.hasProduction ? (
                          <PercentageCircleNewPropostal
                            currentProductType={item.productType}
                            percentage={item.percentageCompleted ?? 0}
                          />
                        ) : (
                          <div className="text-center text-gray-500">
                            Não possui produção
                          </div>
                        )}
                        <h1 className="mt-2 text-lg font-semibold">
                          {item.productType}
                        </h1>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <>
                  {currentPercent !== null ? (
                    <OldPercentage
                      percentage={currentPercent}
                      raio={120}
                      animationSpeed={10}
                      strokeWidth={50}
                      currentProductType={currentProductType ?? ""}
                      showLegend={true}
                    />
                  ) : (
                    <>
                      {!isNewProposal ? (
                        <>
                          <NotRegistered />
                        </>
                      ) : (
                        <NotStartedProduction />
                      )}
                    </>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

const ProductionProgressTable = () => {
  const { proposalData } = useDataContext();
  const [isGrabbing, setIsGrabbing] = useState(false);

  return (
    <button
      className={`w-full flex flex-col relative rounded-xl shadow h-full no-select ${isGrabbing ? "cursor-grabbing" : "cursor-grab"}`}
      style={{
        position: "relative",
        // boxShadow: "0px 0px 5px rgba(0, 0, 0, 0.3)",
      }}
      onMouseDown={() => setIsGrabbing(true)}
      onMouseUp={() => setIsGrabbing(false)}
      onMouseLeave={() => setIsGrabbing(false)}
      tabIndex={-1}
    >
      {" "}
      <div
        className="bg-PormadeGreen w-full text-white text-center  sm:text-start shadow p-4 rounded-t-xl"
        style={{ textShadow: "1px 1px 1px black" }}
      >
        <h1 className="tl380:text-2xl font-bold">Produção</h1>
      </div>
      <section className="flex flex-col sm:flex-row w-full">
        <div className="w-full">
          {proposalData ? (
            <CardProductionProgress />
          ) : (
            <ProductionProgressTableLOading />
          )}
        </div>
      </section>
    </button>
  );
};

const ProductionProgressTableLOading = () => {
  return (
    <>
      <div className="flex bg-bgPrimaryWhite h-[50px]  sm:h-[40px] pl-1 items-center  gap-1 text-black overflow-auto flex-row  justify-center ">
        <p className="absolute mt-10 text-md w-[80%] h-6 sm:text-lg animate-pulseLoading text-black"></p>
      </div>
      <div className="rounded-b-xl h-[450px] py-6 px-3 bg-bgPrimaryWhite sm:py-4 md:py-5">
        <div className="w-full flex flex-row h-full justify-center items-center">
          <div className="w-full h-[100%] flex justify-center items-center  rounded-md">
            <div className="h-[300px] w-[300px] rounded-full animate-pulseLoading"></div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductionProgressTable;
