export const CarrouselComponent = ({
  components,
  activeComponentIndex,
}: {
  components: JSX.Element[];
  activeComponentIndex: number;
}) => {
  return (
    <div className="carousel-container">
      <div
        className="carousel-slide"
        style={{ transform: `translateX(-${activeComponentIndex * 100}%)` }}
      >
        {components.map((component) => (
          <div key={component.key} className="carousel-item">
            {component}
          </div>
        ))}
      </div>
    </div>
  );
};
