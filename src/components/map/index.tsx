"use client";

import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";
import { useTheme } from "@/contexts/theme/themeContext";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import { useMapEventHandlers } from "@/hooks/map/events.hook";
import { ICoordinates } from "@/types/proposal";
import {
  APIProvider,
  ControlPosition,
  Map,
  MapControl,
  Marker,
} from "@vis.gl/react-google-maps";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { FaMap, FaRegEye, FaTruck } from "react-icons/fa";
import { FaHouse } from "react-icons/fa6";
import { ButtonControl } from "./button-control";
import MapPopupOverlay from "./popup";
import "./style.css";

export interface ILocation {
  pointName: string;
  position: {
    lat: number;
    lng: number;
  };
  icon?: string;
}

const getIconWithName = ({ name }: { name: string }): string | undefined => {
  if (name.toLowerCase().includes("pedido")) {
    return "/assets/images/truck.svg";
  } else if (name.toLowerCase().includes("entrega")) {
    return "/assets/images/house.svg";
  } else if (name.toLowerCase().includes("pormade")) {
    return "/assets/images/folha.svg";
  }
  return undefined;
};

const CoordinatesMarkers: React.FC<{
  markerLocations: ICoordinates[];
  onFocusMap: (position: google.maps.LatLngLiteral) => void;
}> = ({ markerLocations: locations, onFocusMap }) => {
  const getLatLtn = ({ position }: { position: ICoordinates }) => {
    return {
      lat: position.latitude,
      lng: position.longitude,
    };
  };

  return (
    <>
      {locations.map((location) => (
        <React.Fragment
          key={location.nome + location.latitude + location.longitude}
        >
          <Marker
            key={location.nome}
            onClick={() => {
              onFocusMap({ ...getLatLtn({ position: location }) });
            }}
            icon={getIconWithName({ name: location.nome })}
            position={getLatLtn({ position: location })}
          />
          <MapPopupOverlay
            key={location.nome + "overlay"}
            position={getLatLtn({ position: location })}
            content={location.nome}
            minZoom={7}
            onClick={() => {
              onFocusMap({ ...getLatLtn({ position: location }) });
            }}
          />
        </React.Fragment>
      ))}
    </>
  );
};

const useDeliveryCoordinates = (): ICoordinates[] => {
  const { proposalData, deliveriesData } = useDataContext();
  const { currentProductionSelected } = useAcessContext();
  const { getCurrentDelivery } = useActiveDelivery();

  const defaultLocation: ICoordinates = {
    nome: "Pormade Portas",
    latitude: -26.202037007128375,
    longitude: -51.112265017035604,
  };

  const lastLocation: ICoordinates = {
    nome: "Endereço entrega",
    latitude: proposalData?.deliveryAddress.lat ?? 0,
    longitude: proposalData?.deliveryAddress.lng ?? 0,
  };

  const deliveryLocation = deliveriesData
    ? getCurrentDelivery({
        delivery: deliveriesData,
        isNew: proposalData?.newProposal,
      })?.localizacao
    : undefined;

  const filteredLocations = [
    defaultLocation,
    lastLocation,
    deliveryLocation,
  ].filter((location): location is ICoordinates => location !== undefined);
  return filteredLocations;
};

const config = {
  googleMapsApiKey: process.env.NEXT_PUBLIC_MAPS_API_KEY,
};

export const NewMap = () => {
  const { proposalData } = useDataContext();

  if (proposalData?.newProposal) {
    return (
      <section className={`w-full h-[500px] p-4 bg-PormadeGray`}>
        <div className="h-full w-full bg-bgPrimaryWhite shadow-lg rounded-lg p-1">
          <APIProvider apiKey={config.googleMapsApiKey ?? ""}>
            <MapComponent />
          </APIProvider>
        </div>
      </section>
    );
  }

  return null;
};

const MapComponent: React.FC = () => {
  const { theme } = useTheme();
  const { proposalData } = useDataContext();
  const locations = useDeliveryCoordinates();
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const {
    adjustMapZoomToBounds,
    setMapFocus,
    onTilesLoaded,
    getPositionWithName,
  } = useMapEventHandlers({ mapRef: mapInstanceRef, locations, theme });
  const [showRoute, setShowRoute] = useState(true);

  // const [directions, setDirections] =
  //   useState<google.maps.DirectionsResult | null>(null);
  const streetViewRef = useRef<google.maps.StreetViewPanorama | null>(null);
  const [isStreetView, setIsStreetView] = useState(false);

  const toggleRoute = () => {
    setShowRoute(!showRoute);
  };

  const calculateRoute = useCallback(() => {
    if (locations.length < 2) return;

    if (
      typeof google === "undefined" ||
      !google.maps ||
      !google.maps.DirectionsService
    )
      return;

    const origin = locations.find(
      (location) => location.nome === "Pormade Portas",
    );

    const destination = locations.find((location) =>
      location.nome.includes("entrega"),
    );

    // if (
    //   !origin ||
    //   !destination ||
    //   !google.maps ||
    //   !google.maps.DirectionsService
    // )
    //   return;

    // const directionsService = new google.maps.DirectionsService();
    // const directionsRenderer = new window.google.maps.DirectionsRenderer();

    // directionsRenderer.setMap(mapInstanceRef.current);

    // const waypoints = locations.slice(1, locations.length - 1).map((loc) => ({
    //   location: new google.maps.LatLng(loc.latitude, loc.longitude),
    //   stopover: true,
    // }));

    // directionsService.route(
    //   {
    //     origin: new google.maps.LatLng(origin.latitude, origin.longitude),
    //     destination: new google.maps.LatLng(
    //       destination.latitude,
    //       destination.longitude,
    //     ),
    //     waypoints: waypoints,
    //     travelMode: google.maps.TravelMode.DRIVING,
    //   },
    //   (result, status) => {
    //     if (status === google.maps.DirectionsStatus.OK && result) {
    //       setDirections(result);
    //     } else {
    //       console.error(`Erro ao buscar direções: ${status}`);
    //     }
    //   },
    // );
  }, [locations]);

  const toggleStreetView = useCallback(() => {
    if (!mapInstanceRef.current) return;

    const pormadePosition = locations.find(
      (location) => location.nome === "Pormade Portas",
    );

    if (pormadePosition) {
      const { latitude, longitude } = pormadePosition;
      if (!streetViewRef.current)
        streetViewRef.current = mapInstanceRef.current.getStreetView();
      streetViewRef.current?.setPosition({ lat: latitude, lng: longitude });
      streetViewRef.current.setOptions({
        linksControl: false,
        panControl: false,
        zoomControl: false,
        addressControl: false,
        clickToGo: false,
        pov: {
          heading: 90,
          pitch: 0,
        },
      });
      streetViewRef.current?.setVisible(!isStreetView);
      setIsStreetView(!isStreetView);
    }
  }, [isStreetView, locations]);

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    function handleWheel(event: WheelEvent) {
      if (event.ctrlKey) {
        event.preventDefault();
        event.stopPropagation();
      }
    }
    container.addEventListener("wheel", handleWheel as EventListener, {
      passive: false,
    });

    return () =>
      container.removeEventListener("wheel", handleWheel as EventListener);
  }, []);

  const fetchLocationsDefaultCenter = ({
    locations,
  }: {
    locations: ICoordinates[];
  }): google.maps.LatLngLiteral | undefined => {
    const defaultLocation = locations.find(
      (location) => location.nome !== "Pormade Portas",
    );
    if (defaultLocation) {
      return {
        lat: defaultLocation.latitude,
        lng: defaultLocation.longitude,
      };
    }
    return locations.length > 0
      ? { lat: locations[0].latitude, lng: locations[0].longitude }
      : undefined;
  };

  useEffect(() => {
    if (showRoute) {
      calculateRoute();
    } else {
      // setDirections(null);
    }
  }, [showRoute, locations, calculateRoute]);

  return (
    <div
      ref={containerRef}
      style={{ overscrollBehavior: "none", height: "100%", width: "100%" }}
    >
      {!proposalData ? (
        <div className="flex w-full relative flex-col gap-10 h-full items-center justify-center">
          <div className="loader"></div>
          <span className="text-textColorPrimary">Carregando o mapa...</span>
        </div>
      ) : (
        <Map
          defaultCenter={fetchLocationsDefaultCenter({ locations })}
          style={{ width: "100%", height: "100%" }}
          defaultZoom={4}
          onTilesLoaded={onTilesLoaded}
          streetViewControl={false}
          cameraControl={false}
          tiltInteractionEnabled={false}
          tilt={0}
          backgroundColor={"var(--primary-color)"}
          clickableIcons={true}
          colorScheme={theme?.toUpperCase() as "LIGHT" | "DARK"}
        >
          <MapControl position={ControlPosition.LEFT_BOTTOM}>
            <div className="flex ml-3 flex-col gap-2">
              <button
                onClick={toggleStreetView}
                className=" flex justify-center items-center border-0 m-0 p-0 text-[14px] text-[--map-control-icon]  font-bold shadow-lg appearance-none relative cursor-pointer bg-[var(--map-control-button)] gap-3 w-[150px] h-[40px]"
              >
                <FaRegEye className="text-[--map-control-icon]" size={20} />
                Ver a fábrica
              </button>
            </div>
          </MapControl>
          <MapControl position={ControlPosition.RIGHT_BOTTOM}>
            <div className="m-[10px] flex flex-col gap-2">
              {getPositionWithName({ name: "Pormade" }) && (
                <ButtonControl
                  icon={
                    <img
                      src="/favicon.png"
                      alt="logo"
                      className="w-[20px] text-[--map-control-icon] h-[20px]"
                    />
                  }
                  onClick={() =>
                    setMapFocus(getPositionWithName({ name: "Pormade" })!)
                  }
                />
              )}
              {getPositionWithName({ name: "Endereço" }) && (
                <ButtonControl
                  icon={
                    <FaHouse className="text-[--map-control-icon]" size={20} />
                  }
                  onClick={() =>
                    setMapFocus(getPositionWithName({ name: "Endereço" })!)
                  }
                />
              )}
              {getPositionWithName({ name: "Pedido" }) && (
                <ButtonControl
                  icon={
                    <FaTruck className="text-[--map-control-icon]" size={20} />
                  }
                  onClick={() =>
                    setMapFocus(getPositionWithName({ name: "Pedido" })!)
                  }
                />
              )}
              {locations.length >= 2 && (
                <ButtonControl
                  icon={
                    <FaMap className="text-[--map-control-icon]" size={20} />
                  }
                  onClick={() => {
                    adjustMapZoomToBounds(mapInstanceRef.current!);
                  }}
                />
              )}
            </div>
          </MapControl>
          <CoordinatesMarkers
            markerLocations={locations}
            onFocusMap={setMapFocus}
          />
        </Map>
      )}
    </div>
  );
};

export default MapComponent;
