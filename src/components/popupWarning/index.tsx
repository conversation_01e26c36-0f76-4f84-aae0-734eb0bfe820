import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import React, { useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { FaExclamationTriangle } from "react-icons/fa";

interface PopupWarningProps {
  stage: string;
  nrProposal: string;
}

const PopupWarning: React.FC<PopupWarningProps> = ({ stage, nrProposal }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [shouldRender, setShouldRender] = React.useState<boolean>(isOpen);
  const { setIsWhatsappOpen } = useDetailsProductContext();
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (stage === "Aprovado" && nrProposal) {
      const fetchCiente = localStorage.getItem(`ciente-aprovado-${nrProposal}`);
      if (!fetchCiente) {
        setIsOpen(true);
      }
    }
  }, [stage, nrProposal]);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      setTimeout(() => setIsAnimating(true), 50);
    }
  }, [isOpen]);

  useEffect(() => {
    const popupElement = document.getElementById("popup");
    if (!popupElement) {
      const toastRoot = document.createElement("div");
      document.body.appendChild(toastRoot);
    }
  }, []);

  const popupElement = document.getElementById("popup");
  useEffect(() => {
    if (popupElement && shouldRender) {
      popupElement.classList.remove("hidden");
    } else if (popupElement) {
      popupElement.classList.add("hidden");
    }
  }, [shouldRender, popupElement]);
  if (!popupElement) return null;

  const onRequestClose = () => {
    localStorage.setItem(`ciente-aprovado-${nrProposal}`, "true");
    setIsOpen(false);

    setTimeout(() => {
      setShouldRender(false);
      onRequestClose();
    }, 300);
  };

  const handleContact = () => {
    setIsOpen(false);
    setIsAnimating(false);
    setTimeout(() => {
      setShouldRender(false);
    }, 300);
    setIsWhatsappOpen(true);
  };

  return ReactDOM.createPortal(
    <div className=" top-0 left-0 right-0 flex justify-center items-center bottom-0 ">
      <div
        style={{ zIndex: 9999 }}
        id="toast-fetch"
        className={`sm:w-[400px] text-textColorPrimary top-[-100px]  border border-borderItemDetailsProposal  backdrop-blur-3xl shadow-lg fixed   flex items-center p-8 sm:p-3 transition-transform duration-300 transform ${
          isAnimating ? "translate-y-[100%]" : "translate-y-[-150%]"
        }`}
        role="alert"
        aria-live="assertive"
      >
        <div className="w-full h-full relative flex justify-center flex-col">
          <div className="flex justify-center items-center">
            <div>
              <h1 className="font-bold text-center text-lg">Atenção</h1>
              <div className="flex justify-center items-center">
                <FaExclamationTriangle className="text-yellow-500 mr-2  w-1/4" />
                <p className="text-sm text-justify">
                  Após o início da produção, não será possível alterar seu
                  pedido. Verifique se todas as informações estão corretas. 😉
                </p>
              </div>
            </div>
          </div>
          <div className="flex text-sm justify-between items-center">
            <button
              onClick={handleContact}
              className=" top-0  right-0  underline rounded px-1"
              aria-label="Fechar alerta"
            >
              Entrar em contato
            </button>
            <button
              onClick={onRequestClose}
              className="ml-auto top-0 right-0  underline rounded px-1"
              aria-label="Fechar alerta"
            >
              Estou ciente
            </button>
          </div>
        </div>
      </div>
    </div>,
    popupElement,
  );
};

export default PopupWarning;
