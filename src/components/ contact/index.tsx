"use client";
import pormadeLogo from "@/assets/images/logo_branco.png";
import { useDataContext } from "@/contexts/data/dataContext";
import { useContact } from "@/hooks/contact/contact.hook";
import { WhatsappWidget } from "../whatsappWigtet";

export const Contact = ({
  homeService = "07:30",
  endService = "18:00",
}: {
  homeService: string;
  endService: string;
}) => {
  const { connectionStatus } = useContact({
    homeService,
    endService,
  });

  const { proposalData } = useDataContext();

  return (
    <div>
      <WhatsappWidget
        phoneNumber="+55 42 *********"
        accountName="Pormade Portas"
        avatar={pormadeLogo.src}
        statusMessage={connectionStatus}
        chatMessage={`Olá, ${proposalData?.customer}, qual é a sua dúvida?`}
        nrProposta={proposalData?.proposal ?? ""}
        client={proposalData?.customer ?? ""}
      />
    </div>
  );
};
