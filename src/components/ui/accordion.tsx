"use client";

import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { motion } from "framer-motion";
import { ChevronDown } from "lucide-react";
import * as React from "react";
import { cn } from "./select";

function useDataState(ref: React.RefObject<HTMLElement>) {
  const [dataState, setDataState] = React.useState("closed");

  React.useEffect(() => {
    if (!ref.current) return;
    const observer = new MutationObserver(() => {
      const newState = ref.current?.getAttribute("data-state") || "closed";
      setDataState(newState);
    });
    observer.observe(ref.current, { attributes: true });
    return () => observer.disconnect();
  }, [ref]);

  return dataState;
}

const Accordion = AccordionPrimitive.Root;

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn("border-b", className)}
    {...props}
  />
));
AccordionItem.displayName = "AccordionItem";

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        "flex flex-1 items-center justify-between py-4 font-medium hover:underline",
        className,
      )}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = "AccordionTrigger";

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => {
  const localRef = React.useRef<HTMLDivElement>(null);
  React.useImperativeHandle(ref, () => localRef.current as HTMLDivElement);

  const dataState = useDataState(localRef);

  const variants = {
    open: { height: "auto", opacity: 1 },
    closed: { height: 0, opacity: 0 },
  };

  return (
    <AccordionPrimitive.Content ref={localRef} forceMount {...props}>
      <motion.div
        variants={variants}
        initial="closed"
        animate={dataState === "open" ? "open" : "closed"}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className={cn("overflow-hidden text-sm", className)}
      >
        <div className="pb-4 pt-0">{children}</div>
      </motion.div>
    </AccordionPrimitive.Content>
  );
});
AccordionContent.displayName = "AccordionContent";

export { Accordion, AccordionContent, AccordionItem, AccordionTrigger };
