import { useDataContext } from "@/contexts/data/dataContext";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { IoSend } from "react-icons/io5";
import bgImage from "./bg-image.png";
import { CheckSVG, CloseSVG, WhatsappSVG } from "./Icons";
import "./styles.css";

export interface WhatsappI {
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
  onSubmit?: (e: React.FormEvent<HTMLFormElement>, value: string) => void;
  onClose?: () => void;
  phoneNumber: string;
  statusMessage?: string;
  chatMessage?: string;
  avatar?: string;
  accountName: string;
  darkMode?: boolean;
  nrProposta: string;
  client: string;
}

const Avatar = ({ src, alt }: { src: string; alt: string }) => (
  <div className="p-2  relative">
    <img
      src={src}
      width="60"
      height="60"
      className="rounded-full align-middle object-contain bg-black w-16 h-16 border border-white/50"
      alt={alt}
    />
    <span className="absolute w-2.5 h-2.5 bg-[#4fbe86] border border-white rounded-full bottom-2 right-2"></span>
  </div>
);

const Header = ({
  accountName,
  statusMessage,
  isDelay,
  onClose,
  avatar,
}: {
  accountName: string;
  statusMessage: string | undefined;
  isDelay: boolean;
  onClose: () => void;
  avatar: string | undefined;
}) => (
  <header className="bg-[#075e54] rounded-y-xl grid items-center p-1 grid-cols-[auto,1fr,auto] gap-1 ">
    <Avatar src={avatar ?? ""} alt="whatsapp-avatar" />
    <div className="flex flex-col text-white p-[0.5rem]">
      <span className="text-[1rem] font-[700]">{accountName}</span>
      <span className="text-[0.8rem] text-[#f0f0f0]">
        {isDelay ? "Digitando..." : statusMessage}
      </span>
    </div>
    <button className="p-[1rem] cursor-pointer items-center" onClick={onClose}>
      <CloseSVG />
    </button>
  </header>
);

const Message = ({
  accountName,
  chatMessage,
  timeNow,
}: {
  accountName: string | undefined;
  chatMessage: string | undefined;
  timeNow: string;
}) => (
  <div className="message-appear p-[7px_14px_6px] bg-white rounded-[0px_8px_8px_0px] relative tl380:max-w-[calc(100%-120px)] z-2 shadow-[0px_1px_0.5px_rgba(0,0,0,0.13)]">
    <span
      style={{
        borderWidth: "0px 20px 20px 0px",
        borderColor: "transparent #fff transparent transparent",
      }}
      className="inline-block absolute w-0 h-0 left-[-10px] top-0 border-solid "
    />
    <span className="text-[13px] font-[700] leading-[18px] text-[#********]  whitespace-pre-wrap">
      {accountName}
    </span>
    <p className="text-[14px] leading-[19px] mt-[4px] text-[#111111] whitespace-pre-wrap ">
      {chatMessage}
    </p>
    <span className="flex mt-[4px]  justify-end text-[12px] leading-[16px] text-[#********]">
      {timeNow}
      <span className="ml-[5px]">
        <CheckSVG />
      </span>
    </span>
  </div>
);

const ErrorMessage = ({
  errorMessage,
  timeNowError,
}: {
  errorMessage: string;
  timeNowError: string;
}) => (
  <div className="message-appear p-[7px_14px_6px] bg-white rounded-[0px_8px_8px_0px] relative max-w-[calc(100%-120px)] z-2 shadow-[0px_1px_0.5px_rgba(0,0,0,0.13)] mt-2">
    <span
      style={{
        borderWidth: "0px 20px 20px 0px",
        borderColor: "transparent #fff transparent transparent",
      }}
      className="inline-block absolute w-0 h-0 left-[-10px] top-0 border-solid "
    />
    <p className="text-[14px] leading-[19px] mt-[4px] text-[#111111] whitespace-pre-wrap">
      {errorMessage}
    </p>
    <span className="flex mt-[4px]  justify-end text-[12px] leading-[16px] text-[#********]">
      {timeNowError}
      <span className="ml-[5px]">
        <CheckSVG />
      </span>
    </span>
  </div>
);

export const WhatsappWidget = ({ ...props }: Readonly<WhatsappI>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isDelay, setIsDelay] = useState(true);
  const [messageValue, setMessageValue] = useState("");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { proposalData } = useDataContext();
  const { isWhatsappOpen } = useDetailsProductContext();

  const chatRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);

  const handleOpen = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    setIsOpen((prev) => !prev);
  }, []);

  useEffect(() => {
    if (isWhatsappOpen) {
      setIsOpen(true);
      inputRef.current?.focus();
    }
  }, [isWhatsappOpen]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (
        chatRef.current &&
        !chatRef.current.contains(event.target as Node) &&
        iconRef.current &&
        !iconRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    },
    [handleClose],
  );

  useEffect(() => {
    if (isOpen && proposalData) {
      document.addEventListener("mousedown", handleClickOutside);
      setTimeout(() => {
        setIsDelay(false);
      }, 1000);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, handleClickOutside, proposalData]);

  const timeNow = useMemo(
    () =>
      new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    [],
  );
  const timeNowError = useMemo(
    () =>
      new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    [],
  );

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setErrorMessage("Recurso em desenvolvimento...");

    if (messageValue.length < 2) {
      setErrorMessage("É necessário enviar uma mensagem válida.");
      return;
    }

    setErrorMessage("");
    const baseUrl = "https://api.whatsapp.com/send/?phone=";
    const phoneNumber = encodeURIComponent(props.phoneNumber);
    const messageText = encodeURIComponent(
      `Olá, meu nome é *${proposalData?.customer}*\nMeu email é ${proposalData?.email}! \nEstou com dúvida referente ao meu pedido _${proposalData?.proposal}_.\nPoderia me ajudar? \n${messageValue}`,
    );

    window.open(`${baseUrl}${phoneNumber}&text=${messageText}`, "_blank");
    setMessageValue("");
    setIsOpen(false);
  };

  return (
    <div className="w-full h-full after:text-left relative">
      <div
        ref={iconRef}
        className="w-[60px] h-[60px] flex justify-center items-center fixed bottom-[2rem] right-[2rem] cursor-pointer text-white bg-[#25d366] rounded-full z-60 no-select shadow-lg after:content-[''] after:border-inherit after:w-[60px] after:h-[60px] after:rounded-full after:absolute after:shadow-md"
        aria-hidden="true"
        onClick={handleOpen}
      >
        <WhatsappSVG />
      </div>
      <div
        ref={chatRef}
        style={{ zIndex: 99999 }}
        className={`flex flex-col justify-between rounded-3xl overflow-hidden bg-white touch-auto fixed bottom-[6rem] right-[1rem] sm:bottom-[7rem] sm:right-[4rem] max-w-[90%] w-[375px] opacity-0 transition-height duration-300 ease-out shadow-lg ${
          isOpen ? " opacity-100 h-[450px]" : "h-0 opacity-0"
        }`}
      >
        <Header
          accountName={props.accountName}
          statusMessage={props.statusMessage}
          isDelay={isDelay}
          onClose={handleClose}
          avatar={props.avatar}
        />
        <div
          className="p-[20px] bg-cover bg-repeat max-h-[402px] bg-[#eae6df] h-full"
          style={{
            backgroundImage: `linear-gradient(rgba(215, 215, 215, 0.8), rgba(215, 215, 215, 0.8)), url(${bgImage.src})`,
          }}
        >
          {isDelay ? (
            <div
              style={{ borderBottomLeftRadius: "2px" }}
              className="bg-[#fff] py-[16px] px-[28px] inline-block"
            >
              <div className="typing flex items-center h-[17px]">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className="dot animate-typing bg-[rgba(145,147,146,0.7)] rounded-full h-[7px] w-[7px] mr-[4px] inline-block"
                  ></div>
                ))}
              </div>
            </div>
          ) : (
            <Message
              accountName={props.accountName}
              chatMessage={props.chatMessage}
              timeNow={timeNow}
            />
          )}
          {errorMessage && (
            <ErrorMessage
              errorMessage={errorMessage}
              timeNowError={timeNowError}
            />
          )}
        </div>
        <footer className="p-[0.25rem] bg-[#f0f0f0]">
          <form
            onSubmit={handleSubmit}
            className="grid items-center grid-cols-[80%,20%]  tl380:grid-cols-[85%,15%] p-[0.25rem] gap-[0.5rem]"
          >
            <input
              ref={inputRef}
              type="text"
              className="rounded-xl px-[20px] py-[10px] text-black border-none bg-white min-h-[45px] focus:outline-none focus:shadow-[inset_0_0_0_1px_#075e54]"
              placeholder="Digite uma mensagem"
              onChange={(e) => setMessageValue(e.target.value)}
              value={messageValue}
            />
            <button
              className="cursor-pointer bg-[#075e54] text-white flex justify-center items-center rounded-full w-[45px] h-[45px] border-0 disabled:opacity-50 disabled:pointer-events-none"
              disabled={!messageValue}
            >
              <IoSend size={24} className="fill-current" />
            </button>
          </form>
        </footer>
      </div>
    </div>
  );
};
