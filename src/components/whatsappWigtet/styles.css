@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.typing .dot {
  animation: typingAnimation 1.8s infinite ease-in-out;
}

.typing .dot:nth-child(1) {
  animation-delay: 200ms;
}

.typing .dot:nth-child(2) {
  animation-delay: 300ms;
}

.typing .dot:nth-child(3) {
  animation-delay: 400ms;
}


.whatsapp-widget-enter {
  transform: translateY(100%);
  opacity: 0;
}

.whatsapp-widget-enter-active {
  transform: translateY(0);
  opacity: 1;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.whatsapp-widget-exit {
  transform: translateY(0);
  opacity: 1;
}

.whatsapp-widget-exit-active {
  transform: translateY(100%);
  opacity: 0;
  transition: transform 0.3s ease-in, opacity 0.3s ease-in;
}


.message-appear {
  opacity: 0;
  transform: translateY(20px);
  animation: messageAppear 0.3s forwards;
}

@keyframes messageAppear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
