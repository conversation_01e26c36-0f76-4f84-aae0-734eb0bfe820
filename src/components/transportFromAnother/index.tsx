import { useAcessContext } from "@/contexts/acess/acessContext";
import { useDataContext } from "@/contexts/data/dataContext";
import { useDetailsProductContext } from "@/contexts/detailsProduct/detailsProductContext";
import { useActiveDelivery } from "@/hooks/deliveriesDetails/useCurrentDeliveries";
import useInitialInformation from "@/hooks/inititalInformation/useInitialInformation";
import { useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { FaExclamationTriangle } from "react-icons/fa";

const TransportFromAnother = ({ nrProposal }: { nrProposal: string }) => {
  const { deliveriesData, proposalData } = useDataContext();
  const { stage } = useInitialInformation();
  const [isOpen, setIsOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState<boolean>(isOpen);
  const { currentProductionSelected } = useAcessContext();
  const { setIsWhatsappOpen } = useDetailsProductContext();
  const { getCurrentDelivery } = useActiveDelivery();

  // const getCurrentDelivery = deliveriesData?.find(
  //   (item) => item.order === currentProductionSelected,
  // );

  // const getCurrentDelivery = useMemo(() => {
  //   if (proposalData?.newProposal) {
  //     return deliveriesData?.find(
  //       (item) => item.order === currentProductionSelected,
  //     );
  //   }

  //   return deliveriesData?.[0];
  // }, [deliveriesData, currentProductionSelected, proposalData?.newProposal]);

  useEffect(() => {
    if (
      getCurrentDelivery({
        delivery: deliveriesData,
        isNew: proposalData?.newProposal,
      })?.deliveryByPartner.isPartner &&
      stage === "Entregue"
    ) {
      const fetchCiente = localStorage.getItem(
        `transport-from-another-${nrProposal}`,
      );
      if (!fetchCiente) setIsOpen(true);
    }
  }, [deliveriesData, stage, nrProposal, getCurrentDelivery, proposalData]);

  const popupElement = document.getElementById(
    "warning-transport-from-another",
  );

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      setTimeout(() => setIsAnimating(true), 50);
    }
  }, [isOpen]);

  const onRequestClose = () => {
    localStorage.setItem(`transport-from-another-${nrProposal}`, "true");
    setIsAnimating(false);
    setTimeout(() => {
      setShouldRender(false);
      setIsOpen(false);
    }, 300);
  };
  useEffect(() => {
    if (popupElement && shouldRender) popupElement.classList.remove("hidden");
    else if (popupElement) popupElement.classList.add("hidden");
  }, [shouldRender, popupElement]);

  const handleContact = () => {
    setIsOpen(false);
    setTimeout(() => {
      setShouldRender(false);
    }, 300);

    setIsWhatsappOpen(true);
  };

  return shouldRender
    ? ReactDOM.createPortal(
        <div className=" top-0 left-0 right-0 flex justify-center items-center bottom-0 ">
          <div
            style={{ zIndex: 9999 }}
            id="toast-fetch"
            className={`sm:w-[400px] text-textColorPrimary top-[-100px] border border-borderItemDetailsProposal backdrop-blur-3xl shadow-lg fixed flex items-center p-8 sm:p-3 transition-transform duration-300 transform ${
              isAnimating ? "translate-y-[100%]" : "translate-y-[-150%]"
            }`}
            role="alert"
            aria-live="assertive"
          >
            <div className="w-full h-full relative flex justify-center flex-col">
              <div className="flex justify-center items-center">
                <div>
                  <h1 className="font-bold text-center text-lg">Atenção</h1>
                  <div className="flex justify-center items-center">
                    <FaExclamationTriangle className="text-yellow-500 mr-2  w-1/4" />
                    <p className="text-sm text-justify">
                      O pedido foi entregue ao transportador terceirizado para
                      realizar a entrega na obra.
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex text-sm justify-between items-center">
                <button
                  onClick={handleContact}
                  className=" top-0  right-0  underline rounded px-1"
                  aria-label="Fechar alerta"
                >
                  Entrar em contato
                </button>
                <button
                  onClick={onRequestClose}
                  className="ml-auto top-0 right-0  underline rounded px-1"
                  aria-label="Fechar alerta"
                >
                  Estou ciente
                </button>
              </div>
            </div>
          </div>
        </div>,
        popupElement!,
      )
    : null;
};

export default TransportFromAnother;
