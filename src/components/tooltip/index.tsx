import React, { useState, useEffect, useRef } from "react";

type TooltipProps = {
  text: string;
  children: React.ReactNode;
};

const Tooltip: React.FC<TooltipProps> = ({ text, children }) => {
  const [visible, setVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement | null>(null);

  const showTooltip = () => setVisible(true);
  const hideTooltip = () => setVisible(false);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      tooltipRef.current &&
      !tooltipRef.current.contains(event.target as Node)
    ) {
      setVisible(false);
    }
  };

  useEffect(() => {
    if (visible) {
      document.addEventListener("click", handleClickOutside);
    } else {
      document.removeEventListener("click", handleClickOutside);
    }

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [visible]);

  return (
    <div
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onClick={() => setVisible(!visible)}
    >
      {visible && (
        <div
          ref={tooltipRef}
          className="absolute left-[-320px] top-[-130%] sm:top-[-200%] sm|:left-[50%] w-[400px] sm:w-[400px] text-textColorPrimary  border border-textColorPrimary bg-bgPrimaryWhite  p-2 rounded shadow-lg"
          // style={{ top: "-200%", left: "50%", transform: "translateX(-50%)" }}
        >
          {text}
        </div>
      )}
      {children}
    </div>
  );
};

export default Tooltip;
