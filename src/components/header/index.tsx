"use client";
import { ButtonLogout } from "./buttonLogout";
import { HeaderTheme } from "./theme";
import { Logo } from "./logo";
import { FaBars, FaTimes } from "react-icons/fa";
import "./styles.css";
import { <PERSON><PERSON><PERSON>, LuSun } from "react-icons/lu";
import { useHeader } from "@/hooks/header/useHeader";
import Notification from "./notifications";

import { useEffect, useState } from "react";
import { useDataContext } from "@/contexts/data/dataContext";

interface HeaderProps {
  className?: string;
}

const createItemDropMenu = (
  icon: JSX.Element,
  label: string,
  onClick: () => void,
) => {
  return (
    <div
      className="w-full justify-between  transition shadow-3xl min-h-30 h-30 flex p-5 cursor-pointer items-center rounded-xl"
      style={{
        transition: "background-color 0.3s linear, color 0.3s ease-in-out",
        boxShadow: "0px 0px 5px var(--shadow-color-card)",
        border: "1px solid var(--border-color-card)",
      }}
      onClick={onClick}
    >
      {icon}
      <p className="text-textColorPrimary sm:block ml-3">{label}</p>
    </div>
  );
};

const DropMenu = ({
  toggleTheme,
  theme,
  openNotifications,
  setOpenNotifications,
}: {
  toggleTheme: () => void;
  theme: string | null;
  openNotifications: boolean;
  setOpenNotifications: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <nav className="p-5 pt-[35%] w-full flex-col gap-5 flex h-full">
      {createItemDropMenu(
        theme === "light" ? (
          <LuMoon className={`text-3xl moonIcon text-black`} />
        ) : (
          <LuSun className={`text-3xl sunIcon text-white`} />
        ),
        theme === "light" ? "Tema Escuro" : "Tema Claro",
        toggleTheme,
      )}
      {createItemDropMenu(
        <Notification
          notificationOpen={openNotifications}
          setNotificationOpen={setOpenNotifications}
        />,
        "Notificações",
        () => setOpenNotifications(!openNotifications),
      )}
      {createItemDropMenu(<ButtonLogout />, "Sair", () => {})}
    </nav>
  );
};

const Header: React.FC<HeaderProps> = ({ className }) => {
  const { isMenuOpen, menuRef, theme, toggleTheme, toggleMenu } = useHeader();
  const [openNotifications, setOpenNotifications] = useState(false);

  const { openNotificationHash } = useDataContext();

  useEffect(() => {
    if (openNotificationHash) {
      setOpenNotifications(true);
    }
  }, [openNotificationHash]);

  return (
    <header
      className={`bg-bgPrimaryWhite w-full flex items-center justify-center py-7 shadow-3xl ${className}`}
    >
      <div className="my-auto w-full">
        <div className="flex justify-between items-center mx-2">
          <div className="ml-2">
            <Logo />
          </div>

          <div className="mr-[50px] h-full hidden flex-row sm:flex">
            <div className="tooltip-container2">
              <span className="tooltip2">Tema</span>
              <HeaderTheme />
            </div>
            <div className="tooltip-container2">
              <span className="tooltip3">Notificações</span>
              <Notification
                notificationOpen={openNotifications}
                setNotificationOpen={setOpenNotifications}
              />
            </div>
            <ButtonLogout />
          </div>

          <div
            style={{ zIndex: 100 }}
            className="sm:hidden flex items-center mr-5 "
          >
            <button
              className="text-3xl text-gray-600 focus:outline-none"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <FaTimes /> : <FaBars />}
            </button>
          </div>

          <div
            ref={menuRef}
            className={`fixed top-0 right-0 h-full w-[250px] bg-black z-50 menu ${isMenuOpen ? "menu-open" : ""}`}
          >
            <DropMenu
              toggleTheme={toggleTheme}
              theme={theme}
              openNotifications={openNotifications}
              setOpenNotifications={setOpenNotifications}
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
