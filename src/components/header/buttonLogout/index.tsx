"use client";
import { useAcessContext } from "@/contexts/acess/acessContext";
import { VscSignOut } from "react-icons/vsc";
import "./styles.css";

export const ButtonLogout = () => {
  const { logout } = useAcessContext();

  return (
    <button
      className="flex justify-between w-full items-center flex-row text-white cursor-pointer logout-container"
      onClick={logout}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === "Space") {
          logout();
        }
      }}
    >
      <VscSignOut className="text-textColorPrimary text-3xl" />
      <span className="tooltip">Sair</span>
    </button>
  );
};
