import NotificationConfig from "@/components/notification-config";
import { IoNotificationsOutline } from "react-icons/io5";

const Notification = ({
  notificationOpen,
  setNotificationOpen,
}: {
  notificationOpen: boolean;
  setNotificationOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  return (
    <div className="flex items-center justify-center mr-8">
      <button
        className={`text-3xl cursor-pointer disabled:opacity-30 theme-button `}
        onClick={() => setNotificationOpen(!notificationOpen)}
      >
        <IoNotificationsOutline className="text-textColorPrimary" />
      </button>

      {notificationOpen && <NotificationConfig setOpen={setNotificationOpen} />}
    </div>
  );
};

export default Notification;
