import { InputProps } from "@/types/auth/accessInput";

export const AccessInput = ({
  id,
  name,
  label,
  value,
  onFocus,
  onBlur,
  onChange,
  showLabel,
  errorMessage,
}: InputProps) => {
  return (
    <div className={`relative`}>
      <input
        type="text"
        id={id}
        name={name}
        value={value}
        onFocus={onFocus}
        onBlur={onBlur}
        onChange={onChange}
        className={`w-full text-textInputLogin bg-bgGrayInputLogin border-b border-transparent p-3 pt-5 pb-2 outline-none border-gray-500 autocomplete`}
      />
      <label
        htmlFor={id}
        className={`
            absolute left-3 transition-all duration-400 ${
              showLabel
                ? "top-1 text-xs text-labelInputLoginActive cursor-text"
                : "top-3 text-base text-labelInputLoginInactive cursor-text"
            }
          `}
      >
        {label}
      </label>
      {errorMessage && (
        <span className="text-red-500 text-sm mt-1">{errorMessage}</span>
      )}
    </div>
  );
};
