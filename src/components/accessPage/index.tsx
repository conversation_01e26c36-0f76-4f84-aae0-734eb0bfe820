"use client";
import { useAcessContext } from "@/contexts/acess/acessContext";
import { UseAccessPage } from "@/hooks/accessPage/useAccessPage";
import ModalExpired from "../modal/modalHashExpired";
import { AccessInput } from "./accessInput";

const AccessForm = () => {
  const {
    handleSubmit,
    setShowCpfCnpjLabel,
    setShowProposalCodeLabel,
    showCpfCnpjLabel,
    showProposalCodeLabel,
    setCpfCnpj,
    setProposalCode,
    cpfCnpj,
    proposalCode,
    handleCnpjCpfChange,
    handleProposalCodeChange,
    isButtonDisabled,
    proposalCodeError,
  } = UseAccessPage();
  const { hashExpired, setHashExpired } = useAcessContext();

  return (
    <>
      <form onSubmit={handleSubmit} className={`space-y-1`}>
        <div className={`space-y-5 mb-8`}>
          <AccessInput
            id="orderId"
            name="orderId"
            label="Número do Pedido"
            value={proposalCode}
            onFocus={() => setShowProposalCodeLabel(true)}
            onBlur={() =>
              proposalCode.length > 0
                ? setShowProposalCodeLabel(true)
                : setShowProposalCodeLabel(false)
            }
            onChange={(e) => {
              setProposalCode(e.target.value);
              handleProposalCodeChange(e);
            }}
            errorMessage={proposalCodeError}
            showLabel={showProposalCodeLabel}
          />
          <AccessInput
            id="cnpj"
            name="cnpj"
            label="CPF/CNPJ"
            value={cpfCnpj}
            onFocus={() => setShowCpfCnpjLabel(true)}
            onBlur={() =>
              cpfCnpj.length > 0
                ? setShowCpfCnpjLabel(true)
                : setShowCpfCnpjLabel(false)
            }
            onChange={(e) => {
              setCpfCnpj(e.target.value);
              handleCnpjCpfChange(e);
            }}
            showLabel={showCpfCnpjLabel}
          />
        </div>
        <button
          type="submit"
          onClick={() => handleSubmit}
          className="w-full bg-PormadeGreen text-textButtonLogin py-4 rounded-md hover:bg-bgButtonLoginHover disabled:opacity-25 font-extrabold"
          style={{
            textShadow: "1px 1px 1px var(--shadow-button-login)",
          }}
          disabled={isButtonDisabled}
        >
          Rastrear Pedido
        </button>
      </form>
      {hashExpired && (
        <ModalExpired
          isOpen={hashExpired}
          onRequestClose={() => setHashExpired(false)}
        />
      )}
    </>
  );
};

export default AccessForm;
