"use client";

import { useTheme } from "@/contexts/theme/themeContext";
import Image from "next/image";
import pormdeLogoBranca from "../../assets/images/logo_branco.png";
import pormadeLogo from "../../assets/images/logo_preta.png";

export const LogoAccess = () => {
  const { theme } = useTheme();

  return (
    <Image
      src={theme === "light" || !theme ? pormadeLogo : pormdeLogoBranca}
      alt="Logo Pormade"
      width={150}
      height={150}
      style={{ objectFit: "contain" }}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      loading="lazy"
    />
  );
};
